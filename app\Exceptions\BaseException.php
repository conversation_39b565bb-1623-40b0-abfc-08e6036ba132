<?php

namespace App\Exceptions;

use App\Enums\ErrorCodeEnum;
use App\Utils\Respond;
use Exception;
use Illuminate\Contracts\Debug\ShouldntReport;

class BaseException extends Exception implements ShouldntReport
{
    protected $errcode;
    protected $errData;
    protected $statusCode;
    protected $errMsg;

    //    const SYSTEM_ERROR = 100000;
    //    const MODEL_NOT_FOUND = 100001;
    //    const NOT_FOUND = 100002;
    //    const METHOD_NOT_ALLOWED = 100003;
    //    const DATABASE_QUERY_EXCEPTION = 100004;
    //    const OTHER_HTTP_EXCEPTION = 100005;
    //    const HAS_SENSITIVE = 100006;
    //    // 参数验证错误
    //    const VALIDATE_ERROR = 100007;

    /**
     * @param $errcode
     * @param $errData
     * @param $statusCode
     */
    public function __construct($errcode = 100000, $errData = [], $statusCode = 400)
    {
        // 判断errcode是否为数字, 如果不是数字,则默认为errMsg, 并且errcode定义为100000
        if ($errcode instanceof ErrorCodeEnum) {
            $errcode = $errcode->value;
        }

        if (!is_numeric($errcode)) {
            $errMsg = $errcode;
            $errcode = 100000;
        }
        $this->errcode = $errcode;
        $this->errData = $errData;
        $this->errMsg = $errMsg ?? static::message($errcode);
        $this->statusCode = $statusCode;
        parent::__construct($this->getErrMsg(), $this->getErrCode());
    }


    public static function message($code)
    {
        return ErrorCodeEnum::from($code)
            ->label();

        //        $msgArr = [
        //            static::SYSTEM_ERROR => '请稍后再试',
        //            static::MODEL_NOT_FOUND => '未找到该项目',
        //            static::NOT_FOUND => '页面不存在',
        //            static::METHOD_NOT_ALLOWED => '不支持的请求方式',
        //            static::DATABASE_QUERY_EXCEPTION => '数据库查询异常',
        //            static::OTHER_HTTP_EXCEPTION => '其他网络错误',
        //            static::HAS_SENSITIVE => '输入信息违规,请修改或重新输入',
        //            static::VALIDATE_ERROR => '参数错误',
        //        ];
        //
        //        return key_exists($code, $msgArr) ? $msgArr[$code] : '未知错误(' . $code . ')';
    }

    public function report()
    {
        return false;
    }

    public function render()
    {
        return Respond::error($this->getErrCode(), $this->getErrMsg(), $this->getErrData(), $this->getStatusCode());
    }

    public function setErrCode($errcode)
    {
        $this->errcode = $errcode;

        return $this;
    }

    public function setStatusCode($statusCode)
    {
        $this->statusCode = $statusCode;

        return $this;
    }

    public function setErrData($errData)
    {
        $this->errData = $errData;

        return $this;
    }

    public function setErrMsg($errMsg)
    {
        $this->errMsg = $errMsg;

        return $this;
    }

    public function getErrCode()
    {
        return $this->errcode;
    }

    public function getStatusCode()
    {
        return $this->statusCode;
    }

    public function getErrData()
    {
        return $this->errData;
    }

    public function getErrMsg()
    {
        return $this->errMsg;
    }

    public static function make(...$parameters)
    {
        return new static(...$parameters);
    }

    public function shouldRenderJsonWhenCallback()
    {
        return true;
    }
}
