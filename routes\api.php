<?php

use App\Http\Controllers\AuthController;
use App\Http\Controllers\ClockController;
use Illuminate\Support\Facades\Route;

// 前台认证路由（不需要认证）
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
    Route::put('refresh', [AuthController::class, 'refresh']);
});

// 前台认证路由（需要认证）
Route::prefix('auth')->middleware('auth:api')->group(function () {
    Route::get('me', [AuthController::class, 'me']);
    Route::delete('logout', [AuthController::class, 'logout']);
});

// 前端API路由组
Route::prefix('clock')->middleware('auth:api')->group(function () {
    // 打卡状态
    Route::get('status', [ClockController::class, 'clockInStatus']);
    // 打卡
    Route::post('in', [ClockController::class, 'clockIn']);
    // 申请补卡
    Route::post('makeup/apply', [ClockController::class, 'applyForMakeup']);
    // 获取打卡记录
    Route::get('records', [ClockController::class, 'getClockRecords']);
    // 获取需要补卡的记录
    Route::get('makeup/records', [ClockController::class, 'getMakeupRecords']);
    // 获取补卡记录
    Route::get('makeup/applied', [ClockController::class, 'getAppliedMakeupRecords']);
});
