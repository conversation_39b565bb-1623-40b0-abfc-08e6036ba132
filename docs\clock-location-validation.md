# 打卡位置验证功能

## 功能概述

打卡系统支持基于地理位置的打卡限制，只允许在指定范围内进行打卡，防止员工在非工作场所打卡。

## 配置说明

### 环境变量配置

在 `.env` 文件中添加以下配置：

```env
# 打卡位置限制配置
CLOCK_RANGE_ENABLE=true                    # 是否启用位置限制
CLOCK_RANGE_CENTER_LATITUDE=39.9042       # 中心点纬度（北京天安门）
CLOCK_RANGE_CENTER_LONGITUDE=116.4074     # 中心点经度（北京天安门）
CLOCK_RANGE_DISTANCE=500                  # 允许的最大距离（米）
```

### 配置文件

在 `config/clock.php` 中的相关配置：

```php
/*
|--------------------------------------------------------------------------
| 打卡地理范围限制
|--------------------------------------------------------------------------
|
| 此配置项定义打卡的地理位置限制
| clock_range_enable: 是否启用位置限制
| clock_range_center_latitude: 中心点纬度
| clock_range_center_longitude: 中心点经度
| clock_range_distance: 允许的最大距离（米）
|
*/
'clock_range_enable' => env('CLOCK_RANGE_ENABLE', false),
'clock_range_center_latitude' => env('CLOCK_RANGE_CENTER_LATITUDE', '39.9042'),
'clock_range_center_longitude' => env('CLOCK_RANGE_CENTER_LONGITUDE', '116.4074'),
'clock_range_distance' => env('CLOCK_RANGE_DISTANCE', 500),
```

## 功能特性

### 1. 距离计算
- 使用 Haversine 公式计算两点间的球面距离
- 精确度高，适用于地球表面的距离计算
- 返回结果单位为米

### 2. 坐标验证
- 验证纬度范围：-90° 到 90°
- 验证经度范围：-180° 到 180°
- 防止无效坐标导致的计算错误

### 3. 灵活配置
- 可通过配置开启/关闭位置限制
- 支持自定义中心点坐标
- 支持自定义允许距离范围

## LocationService 服务类

### 主要方法

#### `calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float`
计算两个地理坐标点之间的距离

```php
use App\Services\LocationService;

$locationService = new LocationService();
$distance = $locationService->calculateDistance(
    39.9042, 116.4074, // 北京天安门
    39.9163, 116.3972  // 北京故宫
);
// 返回距离约 1000 米
```

#### `validateClockLocation(float $userLatitude, float $userLongitude): array`
验证打卡位置是否在允许范围内

```php
$result = $locationService->validateClockLocation(39.9050, 116.4074);
/*
返回结果：
[
    'valid' => true,                    // 是否有效
    'distance' => 89.5,                 // 实际距离（米）
    'max_distance' => 500,              // 最大允许距离（米）
    'center_latitude' => 39.9042,       // 中心点纬度
    'center_longitude' => 116.4074,     // 中心点经度
    'message' => '位置验证通过'           // 验证消息
]
*/
```

#### `getClockLocationConfig(): array`
获取打卡位置配置信息

```php
$config = $locationService->getClockLocationConfig();
/*
返回结果：
[
    'enabled' => true,
    'center_latitude' => 39.9042,
    'center_longitude' => 116.4074,
    'max_distance' => 500
]
*/
```

#### `isValidCoordinate(float $latitude, float $longitude): bool`
验证坐标是否有效

```php
$isValid = $locationService->isValidCoordinate(39.9042, 116.4074); // true
$isValid = $locationService->isValidCoordinate(91, 116.4074);       // false（纬度超出范围）
```

#### `formatDistance(float $distance): string`
格式化距离显示

```php
echo $locationService->formatDistance(500);   // "500米"
echo $locationService->formatDistance(1500);  // "1.5公里"
```

## API 接口变化

### 打卡状态接口 `/api/clock/status`

响应中新增 `location_config` 字段：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "status": false,
        "count": 0,
        "today_clock_records": [],
        "is_valid_time": true,
        "current_period": "all_day",
        "time_ranges": {
            "morning": {"start": "00:00", "end": "00:00"},
            "afternoon": {"start": "00:00", "end": "00:00"}
        },
        "location_config": {
            "enabled": true,
            "center_latitude": 39.9042,
            "center_longitude": 116.4074,
            "max_distance": 500
        }
    }
}
```

### 打卡接口 `/api/clock/in`

位置验证失败时的错误响应：

```json
{
    "code": 180002,
    "message": "距离打卡中心点1200米，超出允许范围500米"
}
```

坐标无效时的错误响应：

```json
{
    "code": 180002,
    "message": "打卡位置坐标无效"
}
```

## 使用场景

### 1. 办公室打卡
```env
# 设置办公室为中心点，允许500米范围内打卡
CLOCK_RANGE_ENABLE=true
CLOCK_RANGE_CENTER_LATITUDE=39.9042
CLOCK_RANGE_CENTER_LONGITUDE=116.4074
CLOCK_RANGE_DISTANCE=500
```

### 2. 工厂/园区打卡
```env
# 设置较大的范围，适用于大型工厂或园区
CLOCK_RANGE_ENABLE=true
CLOCK_RANGE_CENTER_LATITUDE=31.2304
CLOCK_RANGE_CENTER_LONGITUDE=121.4737
CLOCK_RANGE_DISTANCE=2000
```

### 3. 远程办公
```env
# 关闭位置限制，允许任意位置打卡
CLOCK_RANGE_ENABLE=false
```

## 前端集成建议

### 1. 获取用户位置
```javascript
navigator.geolocation.getCurrentPosition(
    function(position) {
        const latitude = position.coords.latitude;
        const longitude = position.coords.longitude;
        // 发送打卡请求
        clockIn(latitude, longitude);
    },
    function(error) {
        console.error('获取位置失败:', error);
    }
);
```

### 2. 显示距离信息
```javascript
// 从打卡状态接口获取位置配置
const locationConfig = response.data.location_config;

if (locationConfig.enabled) {
    // 计算当前位置与中心点的距离
    const distance = calculateDistance(
        userLatitude, userLongitude,
        locationConfig.center_latitude, locationConfig.center_longitude
    );
    
    // 显示距离信息给用户
    if (distance <= locationConfig.max_distance) {
        showMessage(`您距离打卡点${distance}米，可以打卡`);
    } else {
        showMessage(`您距离打卡点${distance}米，超出允许范围${locationConfig.max_distance}米`);
    }
}
```

## 注意事项

1. **GPS精度**：移动设备GPS精度通常在3-5米，建议设置的距离范围不要过小
2. **室内定位**：室内GPS信号较弱，可能影响定位精度
3. **网络定位**：在GPS信号不好时，系统可能使用网络定位，精度较低
4. **隐私保护**：确保用户同意位置信息的收集和使用
5. **错误处理**：合理处理定位失败的情况，提供备用方案

## 测试

运行位置验证功能测试：

```bash
php artisan test tests/Feature/ClockLocationValidationTest.php
```

测试覆盖场景：
- 位置验证开启/关闭
- 范围内/范围外打卡
- 无效坐标处理
- 距离计算准确性
- 配置信息返回
