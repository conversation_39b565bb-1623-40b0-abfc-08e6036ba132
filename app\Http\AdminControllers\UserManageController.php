<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\User;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Role;

class UserManageController extends Controller
{
    /**
     * 获取用户列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getUserList(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request,[
            'keyword' => 'nullable|string|max:50',
            'department_id' => 'nullable|integer',
            'status' => 'nullable|integer|in:0,1',
            'is_admin' => 'nullable|boolean',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'keyword.string' => '关键词格式错误',
            'keyword.max' => '关键词长度不能超过50个字符',
            'department_id.integer' => '部门ID格式错误',
            'status.integer' => '状态格式错误',
            'status.in' => '状态值错误',
            'is_admin.boolean' => '管理员权限格式错误',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        // 获取查询参数
        $keyword = $request->input('keyword');
        $departmentId = $request->input('department_id');
        $status = $request->input('status');
        $isAdmin = $request->input('is_admin');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        // 构建查询
        $query = AdminUser::with('department');

        // 按关键词搜索
        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('true_name', 'like', "%{$keyword}%")
                  ->orWhere('phone', 'like', "%{$keyword}%");
            });
        }

        // 按部门筛选
        if ($departmentId) {
            $query->where('department_id', $departmentId);
        }

        // 按状态筛选
        if ($status !== null) {
            $query->where('status', $status);
        }

        // 按管理员权限筛选
        if ($isAdmin !== null) {
            $query->where('is_admin', $isAdmin);
        }

        // 排序
        $query->orderBy('id', 'desc');

        // 分页
        $users = $query->paginate($perPage, ['*'], 'page', $page);

        // 处理返回数据
        // $result = [
        //     'total' => $users->total(),
        //     'per_page' => $users->perPage(),
        //     'current_page' => $users->currentPage(),
        //     'last_page' => $users->lastPage(),
        //     'data' => []
        // ];

        // foreach ($users as $user) {
        //     $result['data'][] = [
        //         'id' => $user->id,
        //         'name' => $user->name,
        //         'true_name' => $user->true_name,
        //         'avatar' => $user->avatar,
        //         'phone' => $user->phone,
        //         'department_id' => $user->department_id,
        //         'department_name' => $user->department ? $user->department->name : null,
        //         'status' => $user->status,
        //         'status_text' => $user->status === User::STATUS_NORMAL ? '正常' : '禁用',
        //         'is_admin' => $user->is_admin,
        //         'created_at' => $user->created_at->toDateTimeString(),
        //     ];
        // }

        return Respond::success($users);
    }

    /**
     * 获取用户详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getUserDetail(int $id): JsonResponse
    {
        $user = AdminUser::with('department')->find($id);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        // 获取用户角色
        $roles = $user->roles()->get(['id', 'name']);

        $result = [
            'id' => $user->id,
            'name' => $user->name,
            'true_name' => $user->true_name,
            'avatar' => $user->avatar,
            'phone' => $user->phone,
            'gender' => $user->gender,
            'department_id' => $user->department_id,
            'department_name' => $user->department ? $user->department->name : null,
            'status' => $user->status,
            'status_text' => $user->status === User::STATUS_NORMAL ? '正常' : '禁用',
            'is_admin' => $user->is_admin,
            'roles' => $roles,
            'created_at' => $user->created_at->toDateTimeString(),
            'updated_at' => $user->updated_at->toDateTimeString(),
        ];

        return Respond::success($result);
    }

    /**
     * 更新用户状态
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateUserStatus(Request $request,$id): JsonResponse
    {
        $user = AdminUser::find($id);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        // 验证请求参数
        $this->validate($request,[
            'status' => 'required|integer|in:0,1',
        ],[
            'status.required' => '状态不能为空',
            'status.integer' => '状态格式错误',
            'status.in' => '状态值错误',
        ]);


        $status = $request->input('status');

        $user->status = $status;
        $user->save();

        return Respond::success(null, '状态更新成功');
    }

    /**
     * 设置用户管理员权限
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function setAdminPermission(Request $request,$id): JsonResponse
    {
        $user = AdminUser::find($id);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        // 验证请求参数
        $this->validate($request,[
            'is_admin' => 'required|boolean',
        ],[
            'is_admin.required' => '管理员权限不能为空',
            'is_admin.boolean' => '管理员权限格式错误',
        ]);


        $isAdmin = $request->input('is_admin');

        $user->is_admin = $isAdmin;
        $user->save();

        return Respond::success(null, '管理员权限设置成功');
    }

    /**
     * 为用户分配角色
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function assignRoles(Request $request, int $id): JsonResponse
    {
        $user = AdminUser::find($id);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        // 验证请求参数
        $this->validate($request, [
            'roles' => 'required|array',
            'roles.*' => 'integer|exists:roles,id',
        ],[
            'roles.required' => '角色不能为空',
            'roles.array' => '角色格式错误',
            'roles.*.integer' => '角色ID格式错误',
            'roles.*.exists' => '角色不存在',
        ]);

        $roleIds = $request->input('roles');

        // 获取角色
        $roles = Role::whereIn('id', $roleIds)->get();

        // 同步角色
        $user->syncRoles($roles);

        return Respond::success(null, '角色分配成功');
    }

    /**
     * 获取用户的角色
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getUserRoles(int $id): JsonResponse
    {
        $user = AdminUser::find($id);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        $roles = $user->roles()->get(['id', 'name', 'guard_name']);

        return Respond::success($roles);
    }

    /**
     * 获取所有可用角色
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getAllRoles(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'guard_name' => 'nullable|string|in:admin,api',
        ],[
            'guard_name.string' => '守卫名称格式错误',
            'guard_name.in' => '守卫名称值错误',
        ]);

        $guardName = $request->input('guard_name', 'admin');

        $roles = Role::where('guard_name', $guardName)->get(['id', 'name']);

        return Respond::success($roles);
    }
}
