<?php

namespace App\Services;

class LocationService
{
    /**
     * 计算两个地理坐标点之间的距离（使用Haversine公式）
     *
     * @param float $lat1 第一个点的纬度
     * @param float $lon1 第一个点的经度
     * @param float $lat2 第二个点的纬度
     * @param float $lon2 第二个点的经度
     * @return float 距离（米）
     */
    public function calculateDistance(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        // 地球半径（米）
        $earthRadius = 6371000;
        
        // 将角度转换为弧度
        $lat1Rad = deg2rad($lat1);
        $lon1Rad = deg2rad($lon1);
        $lat2Rad = deg2rad($lat2);
        $lon2Rad = deg2rad($lon2);
        
        // 计算差值
        $deltaLat = $lat2Rad - $lat1Rad;
        $deltaLon = $lon2Rad - $lon1Rad;
        
        // Haversine公式
        $a = sin($deltaLat / 2) * sin($deltaLat / 2) +
             cos($lat1Rad) * cos($lat2Rad) *
             sin($deltaLon / 2) * sin($deltaLon / 2);
        
        $c = 2 * atan2(sqrt($a), sqrt(1 - $a));
        
        // 计算距离
        $distance = $earthRadius * $c;
        
        return round($distance, 2);
    }
    
    /**
     * 验证打卡位置是否在允许范围内
     *
     * @param float $userLatitude 用户纬度
     * @param float $userLongitude 用户经度
     * @return array 验证结果
     */
    public function validateClockLocation(float $userLatitude, float $userLongitude): array
    {
        // 检查是否启用位置限制
        $rangeEnabled = config('clock.clock_range_enable', false);
        
        if (!$rangeEnabled) {
            return [
                'valid' => true,
                'distance' => 0,
                'max_distance' => 0,
                'center_latitude' => 0,
                'center_longitude' => 0,
                'message' => '位置限制未启用'
            ];
        }
        
        // 获取中心点坐标
        $centerLatitude = (float) config('clock.clock_range_center_latitude', 39.9042);
        $centerLongitude = (float) config('clock.clock_range_center_longitude', 116.4074);
        $maxDistance = (float) config('clock.clock_range_distance', 500);
        
        // 计算距离
        $distance = $this->calculateDistance(
            $userLatitude,
            $userLongitude,
            $centerLatitude,
            $centerLongitude
        );
        
        $isValid = $distance <= $maxDistance;
        
        return [
            'valid' => $isValid,
            'distance' => $distance,
            'max_distance' => $maxDistance,
            'center_latitude' => $centerLatitude,
            'center_longitude' => $centerLongitude,
            'message' => $isValid 
                ? '位置验证通过' 
                : "距离打卡中心点{$distance}米，超出允许范围{$maxDistance}米"
        ];
    }
    
    /**
     * 获取打卡位置配置信息
     *
     * @return array
     */
    public function getClockLocationConfig(): array
    {
        return [
            'enabled' => config('clock.clock_range_enable', false),
            'center_latitude' => (float) config('clock.clock_range_center_latitude', 39.9042),
            'center_longitude' => (float) config('clock.clock_range_center_longitude', 116.4074),
            'max_distance' => (float) config('clock.clock_range_distance', 500),
        ];
    }
    
    /**
     * 格式化距离显示
     *
     * @param float $distance 距离（米）
     * @return string
     */
    public function formatDistance(float $distance): string
    {
        if ($distance >= 1000) {
            return round($distance / 1000, 2) . '公里';
        }
        
        return round($distance, 0) . '米';
    }
    
    /**
     * 验证坐标是否有效
     *
     * @param float $latitude 纬度
     * @param float $longitude 经度
     * @return bool
     */
    public function isValidCoordinate(float $latitude, float $longitude): bool
    {
        // 纬度范围：-90 到 90
        if ($latitude < -90 || $latitude > 90) {
            return false;
        }
        
        // 经度范围：-180 到 180
        if ($longitude < -180 || $longitude > 180) {
            return false;
        }
        
        return true;
    }
    
    /**
     * 获取两点之间的方位角
     *
     * @param float $lat1 起点纬度
     * @param float $lon1 起点经度
     * @param float $lat2 终点纬度
     * @param float $lon2 终点经度
     * @return float 方位角（度）
     */
    public function calculateBearing(float $lat1, float $lon1, float $lat2, float $lon2): float
    {
        $lat1Rad = deg2rad($lat1);
        $lat2Rad = deg2rad($lat2);
        $deltaLonRad = deg2rad($lon2 - $lon1);
        
        $y = sin($deltaLonRad) * cos($lat2Rad);
        $x = cos($lat1Rad) * sin($lat2Rad) - sin($lat1Rad) * cos($lat2Rad) * cos($deltaLonRad);
        
        $bearingRad = atan2($y, $x);
        $bearingDeg = rad2deg($bearingRad);
        
        // 转换为0-360度范围
        return fmod($bearingDeg + 360, 360);
    }
    
    /**
     * 获取方位角对应的方向描述
     *
     * @param float $bearing 方位角（度）
     * @return string
     */
    public function getBearingDirection(float $bearing): string
    {
        $directions = [
            '北', '东北', '东', '东南', '南', '西南', '西', '西北'
        ];
        
        $index = round($bearing / 45) % 8;
        
        return $directions[$index];
    }
}
