<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Models\Department;
use App\Models\User;
use App\Utils\Respond;
use Changguan\LaravelSDK\Facades\Changguan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;
use Str;

class AuthController extends Controller
{
    /**
     * 前台用户登录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function login(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request,[
            'code' => 'required|string',
        ],[
            'code.required' => 'code不能为空',
            'code.string' => 'code格式错误',
        ]);

        try {
            // 使用第三方SDK进行授权认证
            //response: {"errcode":0,"msg":"success","data":{"open_id":"Jhb_37327ff36e64b28880efe352ebccd75be297","union_id":"98137442-a23e-4a75-a51a-3ffa62044602","nickname":"","true_name":"test","display_name":"","mobile":"13801138017","shot_mobile":"","avatar":"https://kczliveoss.cztv.tv/static/images/default_avatar.jpg","departments":[{"id":"55","name":"中吴网","code":"ZWW","parent":0}],"front_user":{"nickname":"笑死C","avatar":"https://kczliveoss.cztv.tv/cguc/20250116/7f656ecd-488a-4c11-b52e-ad0c29f6a075.jpg","gender":"男"}}}
            
            $response = Changguan::oauth()->getUserInfoByCode($request->input('code'));

            // 检查响应是否成功
            if ($response['errcode'] !== 0) {
                return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '授权失败: ' . ($response['msg'] ?? '未知错误'));
            }

            // 获取用户信息
            $userData = $response['data'];

            // 查找或创建用户
            $user = User::where('unionid', $userData['union_id'])
                        ->first();

            if(!$user){
                return Respond::error(ErrorCodeEnum::USER_NOT_FOUND);
            }

            /* if (!$user) {
                // 创建新用户
                $department = Department::firstOrCreate([
                    'code' => $userData['departments'][0]['code'],
                ], [
                    'code' => $userData['departments'][0]['code'],
                    'name' => $userData['departments'][0]['name'],
                    // 'parent_id' => null,
                    // 'level' => 0,
                ]);

                $user = new User([
                    'password' => bcrypt(Str::uuid()),
                    'name' => $userData['display_name']?$userData['display_name']:$userData['true_name'],
                    'true_name' => $userData['true_name'],
                    'phone' => $userData['mobile'],
                    'openid' => $userData['open_id'],
                    'front_openid' => $userData['open_id'],
                    'unionid' => $userData['union_id'] ?? null,
                    'avatar' => $userData['avatar'] ?? null,
                    'gender' => $userData['gender'] ?? null,
                    'status' => User::STATUS_NORMAL,
                    'is_admin' => false,
                ]);
                $user->save();
            } else {
                // 更新用户信息
                $user->front_openid = $userData['open_id'];
                $user->unionid = $userData['union_id'] ?? $user->unionid;
                $user->name = $userData['display_name']?$userData['display_name']:$userData['true_name'];
                $user->phone = $userData['mobile'];
                $user->true_name = $userData['true_name'];
                $user->avatar = $userData['avatar'] ?? $user->avatar;
                $user->gender = $userData['gender'] ?? $user->gender;
                $user->save();
            } */

            // 检查用户状态
            if (!$user->isActive()) {
                return Respond::error(ErrorCodeEnum::USER_STATUS_ERROR, '账号已被禁用');
            }

            // 生成JWT令牌
            $token = Auth::guard('api')->login($user);

            // 返回令牌和用户信息
            return Respond::success([
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => config('jwt.ttl', 60) * 60, // 转换为秒
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'avatar' => $user->avatar,
                    'gender' => $user->gender,
                ]
            ], '登录成功');

        } catch (\Exception $e) {
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前用户信息
     *
     * @return JsonResponse
     */
    public function me(): JsonResponse
    {
        $user = Auth::guard('api')->user();

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN);
        }

        return Respond::success([
            'id' => $user->id,
            'name' => $user->true_name,
            'avatar' => $user->avatar,
            'gender' => $user->gender,
            'phone' => $user->phone,
            'department' => $user->department,
        ]);
    }

    /**
     * 刷新令牌
     *
     * @return JsonResponse
     */
    public function refresh(): JsonResponse
    {
        try {
            $token = auth('api')->refresh();

            return Respond::success([
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => config('jwt.ttl', 60) * 60, // 转换为秒
            ], '刷新成功');
        } catch (\Exception $e) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN, '令牌已过期，请重新登录');
        }
    }

    /**
     * 退出登录
     *
     * @return JsonResponse
     */
    public function logout(): JsonResponse
    {
        Auth::guard('api')->logout();

        return Respond::success(null, '退出成功');
    }
}
