<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Log;

class SyncController extends Controller
{
    /**
     * 同步部门数据
     *
     * @return JsonResponse
     */
    public function syncDepartment(): JsonResponse
    {
        try {
            // 使用静默方式调用Artisan命令
            $exitCode = Artisan::call('sync:contacts', ['type' => 'department', '--lesslog' => true]);
            
            if ($exitCode !== 0) {
                Log::error('部门同步命令执行失败', ['exit_code' => $exitCode]);
                return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '部门同步命令执行失败');
            }
            
            Log::info('通过API触发部门同步');
            return Respond::success(null, '部门同步任务已提交到队列');
        } catch (\Exception $e) {
            Log::error('部门同步命令执行异常: ' . $e->getMessage());
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '部门同步命令执行异常: ' . $e->getMessage());
        }
    }

    /**
     * 同步用户数据
     *
     * @return JsonResponse
     */
    public function syncUser(): JsonResponse
    {
        try {
            // 使用静默方式调用Artisan命令
            $exitCode = Artisan::call('sync:contacts', ['type' => 'user', '--lesslog' => true]);
            
            if ($exitCode !== 0) {
                Log::error('用户同步命令执行失败', ['exit_code' => $exitCode]);
                return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '用户同步命令执行失败');
            }
            
            Log::info('通过API触发用户同步');
            return Respond::success(null, '用户同步任务已提交到队列');
        } catch (\Exception $e) {
            Log::error('用户同步命令执行异常: ' . $e->getMessage());
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '用户同步命令执行异常: ' . $e->getMessage());
        }
    }
}
