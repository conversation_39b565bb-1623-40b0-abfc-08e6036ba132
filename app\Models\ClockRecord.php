<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @mixin IdeHelperClockRecord
 */
class ClockRecord extends Model
{
    use HasFactory;

    /**
     * 可批量赋值的属性
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'clock_time',
        'latitude',
        'longitude',
        'status',
        'apply_status',
        'apply_time',
        'apply_reason',
        'review_time',
        'review_user_id',
        'review_remark',
    ];

    /**
     * 应该被转换为日期的属性
     *
     * @var array
     */
    // protected $dates = [
    //     'clock_time',
    //     'apply_time',
    //     'review_time',
    //     'created_at',
    //     'updated_at',
    // ];

    protected $casts = [
        'clock_time' => 'datetime',
        'apply_time' => 'datetime',
        'review_time' => 'datetime',
    ];

    protected $appends = [
        'status_text',
        'apply_status_text',
    ];

    /**
     * 状态常量：正常打卡
     */
    public const STATUS_NORMAL = 1;

    /**
     * 状态常量：补卡
     */
    public const STATUS_MAKEUP = 2;


    public static $statusMap = [
        self::STATUS_NORMAL => '正常打卡',
        self::STATUS_MAKEUP => '补卡',
    ];

    /**
     * 补卡申请状态常量：无需补卡
     */
    public const APPLY_STATUS_NONE = 0;

    /**
     * 补卡申请状态常量：待处理
     */
    public const APPLY_STATUS_PENDING = 1;

    /**
     * 补卡申请状态常量：已申请
     */
    public const APPLY_STATUS_APPLIED = 2;

    /**
     * 补卡申请状态常量：已补卡
     */
    public const APPLY_STATUS_COMPLETED = 3;

    public static $applyStatusMap = [
        self::APPLY_STATUS_NONE => '无需补卡',
        self::APPLY_STATUS_PENDING => '待处理',
        self::APPLY_STATUS_APPLIED => '已申请',
        self::APPLY_STATUS_COMPLETED => '已补卡',
    ];

    protected function statusText(): Attribute
    {
        return new Attribute(
            get: fn ($value) => self::$statusMap[$this->status],
        );
    }

    protected function applyStatusText(): Attribute
    {
        return new Attribute(
            get: fn ($value) => self::$applyStatusMap[$this->apply_status],
        );
    }

    /**
     * 获取关联的用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取关联的审核人
     */
    public function reviewer(): BelongsTo
    {
        return $this->belongsTo(User::class, 'review_user_id');
    }

    /**
     * 查询指定日期的打卡记录
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string|Carbon $date
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfDate($query, $date)
    {
        $date = $date instanceof Carbon ? $date : Carbon::parse($date);
        return $query->whereDate('clock_time', $date->toDateString());
    }

    /**
     * 查询指定月份的打卡记录
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param string|Carbon $month 格式：Y-m
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfMonth($query, $month)
    {
        if ($month instanceof Carbon) {
            $year = $month->year;
            $month = $month->month;
        } else {
            list($year, $month) = explode('-', $month);
        }
        
        return $query->whereYear('clock_time', $year)
                     ->whereMonth('clock_time', $month);
    }

    /**
     * 查询指定用户的打卡记录
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int $userId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
