<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Role;
use Spatie\Permission\Models\Permission;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清除缓存
        app()[\Spatie\Permission\PermissionRegistrar::class]->forgetCachedPermissions();

        // 创建权限
        // 用户管理权限
        Permission::create(['name' => '查看用户', 'guard_name' => 'admin']);
        Permission::create(['name' => '创建用户', 'guard_name' => 'admin']);
        Permission::create(['name' => '编辑用户', 'guard_name' => 'admin']);
        Permission::create(['name' => '删除用户', 'guard_name' => 'admin']);
        Permission::create(['name' => '修改用户状态', 'guard_name' => 'admin']);
        Permission::create(['name' => '设置管理员', 'guard_name' => 'admin']);

        // 部门管理权限
        Permission::create(['name' => '查看部门', 'guard_name' => 'admin']);
        Permission::create(['name' => '创建部门', 'guard_name' => 'admin']);
        Permission::create(['name' => '编辑部门', 'guard_name' => 'admin']);
        Permission::create(['name' => '删除部门', 'guard_name' => 'admin']);

        // 打卡管理权限
        Permission::create(['name' => '查看打卡记录', 'guard_name' => 'admin']);
        Permission::create(['name' => '审核补卡申请', 'guard_name' => 'admin']);

        // 角色管理权限
        Permission::create(['name' => '查看角色', 'guard_name' => 'admin']);
        Permission::create(['name' => '创建角色', 'guard_name' => 'admin']);
        Permission::create(['name' => '编辑角色', 'guard_name' => 'admin']);
        Permission::create(['name' => '删除角色', 'guard_name' => 'admin']);

        // 权限管理权限
        Permission::create(['name' => '查看权限', 'guard_name' => 'admin']);
        Permission::create(['name' => '分配权限', 'guard_name' => 'admin']);

        // 创建角色并分配权限
        // 超级管理员角色
        $superAdminRole = Role::create(['name' => '超级管理员', 'guard_name' => 'admin']);
        // 超级管理员拥有所有权限
        $superAdminRole->givePermissionTo(Permission::where('guard_name', 'admin')->get());

        // 管理员角色
        $adminRole = Role::create(['name' => '管理员', 'guard_name' => 'admin']);
        // 管理员拥有除了角色和权限管理外的所有权限
        $adminRole->givePermissionTo([
            '查看用户', '创建用户', '编辑用户', '修改用户状态',
            '查看部门', '创建部门', '编辑部门', '删除部门',
            '查看打卡记录', '审核补卡申请'
        ]);

        // 打卡管理员角色
        $clockManagerRole = Role::create(['name' => '打卡管理员', 'guard_name' => 'admin']);
        // 打卡管理员只有打卡相关权限
        $clockManagerRole->givePermissionTo([
            '查看打卡记录', '审核补卡申请'
        ]);

        // 部门管理员角色
        $deptManagerRole = Role::create(['name' => '部门管理员', 'guard_name' => 'admin']);
        // 部门管理员只有部门和用户查看权限
        $deptManagerRole->givePermissionTo([
            '查看用户',
            '查看部门',
            '查看打卡记录'
        ]);
    }
}
