<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Models\User;
use App\Utils\Respond;
use Changguan\LaravelSDK\Facades\Changguan;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Validator;

class AuthController extends Controller
{
    /**
     * 后台用户登录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function login(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request,[
            'code' => 'required|string',
        ]);
        
        try {
            // 使用第三方SDK进行授权认证，注意这里使用admin项目配置
            $response = Changguan::oauth()->getUserInfoByCode($request->input('code'));

            // 检查响应是否成功
            if ($response['errcode'] !== 0) {
                return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '授权失败: ' . ($response['msg'] ?? '未知错误'));
            }

            // 获取用户信息
            $userData = $response['data'];

            // 查找或创建用户
            $user = AdminUser::where('admin_openid', $userData['open_id'])
                ->orWhere('unionid', $userData['union_id'])
                ->first();

            if(!$user){
                return Respond::error(ErrorCodeEnum::USER_NOT_FOUND);
            }

            /* if (!$user) {
                // 创建新用户
                $user = new AdminUser([
                    'name' => $userData['display_name'] ?? $userData['true_name'] ?? '管理员' . substr($userData['open_id'], -6),
                    'true_name' => $userData['true_name'] ?? null,
                    'openid' => $userData['open_id'],
                    'admin_openid' => $userData['open_id'],
                    'unionid' => $userData['union_id'] ?? null,
                    'avatar' => $userData['avatar'] ?? null,
                    'phone' => $userData['mobile'] ?? null,
                    'status' => AdminUser::STATUS_NORMAL,
                    'is_admin' => true, // 默认设置为管理员
                ]);

                // 如果有部门信息，设置部门ID
                if (!empty($userData['departments']) && is_array($userData['departments'])) {
                    $department = $userData['departments'][0] ?? null;
                    if ($department && isset($department['id'])) {
                        $user->department_id = $department['id'];
                    }
                }

                $user->save();
            } else {
                // 更新用户信息
                $user->admin_openid = $userData['open_id'];
                $user->unionid = $userData['union_id'] ?? $user->unionid;
                $user->true_name = $userData['true_name'] ?? $user->true_name;
                $user->name = $userData['display_name'] ?? $userData['true_name'] ?? $user->name;
                $user->avatar = $userData['avatar'] ?? $user->avatar;
                $user->phone = $userData['mobile'] ?? $user->phone;

                // 如果有部门信息，更新部门ID
                if (!empty($userData['departments']) && is_array($userData['departments'])) {
                    $department = $userData['departments'][0] ?? null;
                    if ($department && isset($department['id'])) {
                        $user->department_id = $department['id'];
                    }
                }

                // 如果前台用户信息存在，同步更新
                if (!empty($userData['front_user'])) {
                    $frontUser = $userData['front_user'];
                    $user->name = $frontUser['nickname'] ?? $user->name;
                    $user->avatar = $frontUser['avatar'] ?? $user->avatar;
                    $user->gender = $frontUser['gender'] ?? $user->gender;
                }

                $user->save();
            } */

            // 检查用户状态和权限
            if (!$user->isActive()) {
                return Respond::error(ErrorCodeEnum::USER_STATUS_ERROR, '账号已被禁用');
            }

            if (!$user->canLoginAdmin()) {
                return Respond::error(ErrorCodeEnum::USER_STATUS_ERROR, '您没有权限访问后台');
            }

            // 生成JWT令牌
            $token = Auth::guard('admin')->login($user);
            
            // 返回令牌和用户信息
            return Respond::success([
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => config('jwt_admin.ttl', 60) * 60, // 转换为秒
                'user' => [
                    'id' => $user->id,
                    'name' => $user->name,
                    'true_name' => $user->true_name,
                    'avatar' => $user->avatar,
                    'phone' => $user->phone,
                    'department_id' => $user->department_id,
                    'is_admin' => $user->is_admin,
                ]
            ], '登录成功');
        } catch (\Exception $e) {
            return Respond::error(ErrorCodeEnum::SYSTEM_ERROR, '登录失败: ' . $e->getMessage());
        }
    }

    /**
     * 获取当前管理员信息
     *
     * @return JsonResponse
     */
    public function me(): JsonResponse
    {
        $user = Auth::guard('admin')->user();

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN);
        }

        return Respond::success([
            'id' => $user->id,
            'name' => $user->name,
            'true_name' => $user->true_name,
            'avatar' => $user->avatar,
            'phone' => $user->phone,
            'department_id' => $user->department_id,
            'is_admin' => $user->is_admin,
        ]);
    }

    /**
     * 刷新令牌
     *
     * @return JsonResponse
     */
    public function refresh(): JsonResponse
    {
        try {
            $token = Auth::guard('admin')->refresh(true);

            return Respond::success([
                'token' => $token,
                'token_type' => 'bearer',
                'expires_in' => config('jwt_admin.ttl', 60) * 60, // 转换为秒
            ], '刷新成功');
        } catch (\Exception $e) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN, '令牌已过期，请重新登录');
        }
    }

    /**
     * 退出登录
     *
     * @return JsonResponse
     */
    public function logout(): JsonResponse
    {
        Auth::guard('admin')->logout();

        return Respond::success(null, '退出成功');
    }


}
