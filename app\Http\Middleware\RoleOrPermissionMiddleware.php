<?php

namespace App\Http\Middleware;

use App\Enums\ErrorCodeEnum;
use App\Utils\Respond;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Spatie\Permission\Exceptions\UnauthorizedException;

class RoleOrPermissionMiddleware
{
    /**
     * 处理传入的请求
     *
     * @param Request $request
     * @param Closure $next
     * @param string|array $roleOrPermission
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $roleOrPermission)
    {
        // 检查用户是否已认证
        if (!Auth::guard('admin')->check()) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN, '请先登录');
        }

        $user = Auth::guard('admin')->user();

        // 将参数转换为数组
        $rolesOrPermissions = is_array($roleOrPermission)
            ? $roleOrPermission
            : explode('|', $roleOrPermission);

        // 检查用户是否有任一角色或权限
        if (!$user->hasAnyRole($rolesOrPermissions) && !$user->hasAnyPermission($rolesOrPermissions)) {
            return Respond::error(ErrorCodeEnum::USER_STATUS_ERROR, '您没有权限执行此操作');
        }

        return $next($request);
    }
}
