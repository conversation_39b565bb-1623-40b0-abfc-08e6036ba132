<?php

use App\Enums\ErrorCodeEnum;
use App\Http\Middleware\CheckAdminPermission;
use App\Utils\Respond;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Support\Facades\Route;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        // api: __DIR__ . '/../routes/api.php',
        web: __DIR__ . '/../routes/web.php',
        commands: __DIR__ . '/../routes/console.php',
        then: function () {
            // 前台API路由
            Route::middleware(['api'])
                ->prefix('api')
                ->name('api.')
                ->group(base_path('routes/api.php'));

            // 后台API路由
            Route::middleware(['api'])
                ->prefix('admin/api')
                ->name('admin.')
                ->group(base_path('routes/admin.php'));
        }
    )
    ->withMiddleware(function (Middleware $middleware) {
        // 注册中间件别名
        $middleware->alias([
            'admin' => CheckAdminPermission::class,
            'role' => \App\Http\Middleware\RoleOrPermissionMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions) {
        $exceptions->render(function (\Illuminate\Validation\ValidationException $e) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR->value, $e->validator->errors()
                ->first(), $e->validator->errors()
                ->toArray(), 422);
        })
            ->render(function (\Symfony\Component\HttpKernel\Exception\NotFoundHttpException|\Illuminate\Database\Eloquent\ModelNotFoundException|\Symfony\Component\Routing\Exception\RouteNotFoundException $e) {
                return Respond::error(ErrorCodeEnum::NOT_FOUND->value, ErrorCodeEnum::NOT_FOUND->label(), [], 404);
            })
            ->render(function (\Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException $e) {
                return Respond::error(ErrorCodeEnum::METHOD_NOT_ALLOWED->value, ErrorCodeEnum::METHOD_NOT_ALLOWED->label(), [], 405);
            })
            ->render(function (\Illuminate\Auth\AuthenticationException $e) {
                return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN->value, ErrorCodeEnum::USER_NOT_LOGIN->label(), [], 401);
            });
    })->create();
