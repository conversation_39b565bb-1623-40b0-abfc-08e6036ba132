<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('clock_records', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id');
            $table->timestamp('clock_time')->nullable()->comment('打卡时间');
            $table->decimal('latitude', 10, 7)->nullable()->comment('纬度');
            $table->decimal('longitude', 10, 7)->nullable()->comment('经度');
            $table->tinyInteger('status')->default(1)->comment('状态：1=正常打卡，2=补卡');
            $table->tinyInteger('apply_status')->default(0)->comment('补卡申请状态：0=无需补卡，1=待处理，2=已申请，3=已补卡');
            $table->timestamp('apply_time')->nullable()->comment('补卡申请时间');
            $table->string('apply_reason', 255)->nullable()->comment('补卡申请原因');
            $table->timestamp('review_time')->nullable()->comment('审核时间');
            $table->foreignId('review_user_id')->nullable()->comment('审核人ID');
            $table->string('review_remark', 255)->nullable()->comment('审核备注');
            $table->timestamps();
            
            // 添加索引
            $table->index('user_id');
            $table->index('clock_time');
            $table->index(['user_id', 'clock_time']);
            $table->index('apply_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('clock_records');
    }
};
