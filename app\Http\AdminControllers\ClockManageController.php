<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\ClockRecord;
use App\Models\User;
use App\Models\Department;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ClockManageController extends Controller
{
    /**
     * 获取所有用户打卡记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getClockRecords(Request $request): JsonResponse
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'month' => 'nullable|date_format:Y-m',
            'user_id' => 'nullable|integer',
            'status' => 'nullable|integer|in:1,2',
            'apply_status' => 'nullable|integer|in:0,1,2,3',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'month.date_format' => '日期格式错误',
            'user_id.integer' => '用户ID格式错误',
            'status.integer' => '状态格式错误',
            'status.in' => '状态值错误',
            'apply_status.integer' => '申请状态格式错误',
            'apply_status.in' => '申请状态值错误',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        if ($validator->fails()) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, $validator->errors()->first());
        }
        
        // 获取查询参数
        $month = $request->input('month')?? Carbon::now()->format('Y-m');
        $userId = $request->input('user_id');
        $status = $request->input('status');
        $applyStatus = $request->input('apply_status');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        
        // 查询打卡记录
        $query = ClockRecord::with(['user', 'reviewer'])
            ->ofMonth($month);
        
        // 按用户ID筛选
        if ($userId) {
            $query->where('user_id', $userId);
        }
        
        // 按状态筛选
        if ($status) {
            $query->where('status', $status);
        }
        
        // 按申请状态筛选
        if ($applyStatus !== null) {
            $query->where('apply_status', $applyStatus);
        }
        
        // 排序
        $query->orderBy('clock_time', 'desc');
        
        // 分页
        $records = $query->paginate($perPage, ['*'], 'page', $page);
        
        return Respond::success($records);
        // 获取每日需要打卡的次数
        // $dailyClockCount = config('clock.daily_count', 2);
        
        // // 处理返回数据
        // $result = [];
        
        // foreach ($records as $record) {
        //     $result[] = [
        //         'id' => $record->id,
        //         'user_id' => $record->user_id,
        //         'user_name' => $record->user ? $record->user->name : '未知用户',
        //         'clock_time' => $record->clock_time->toDateTimeString(),
        //         'clock_date' => $record->clock_time->toDateString(),
        //         'status' => $record->status,
        //         'status_text' => $record->status == ClockRecord::STATUS_NORMAL ? '正常打卡' : '补卡',
        //         'apply_status' => $record->apply_status,
        //         'apply_status_text' => $this->getApplyStatusText($record->apply_status),
        //         'latitude' => $record->latitude,
        //         'longitude' => $record->longitude,
        //         'apply_time' => $record->apply_time ? $record->apply_time->toDateTimeString() : null,
        //         'apply_reason' => $record->apply_reason,
        //         'review_time' => $record->review_time ? $record->review_time->toDateTimeString() : null,
        //         'review_user_id' => $record->review_user_id,
        //         'review_remark' => $record->review_remark,
        //     ];
        // }
        
        // return Respond::success([
        //     'items' => $result,
        //     'pagination' => [
        //         'total' => $records->total(),
        //         'per_page' => $records->perPage(),
        //         'current_page' => $records->currentPage(),
        //         'last_page' => $records->lastPage(),
        //     ],
        // ]);
    }
    
    /**
     * 审核补卡申请
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function reviewMakeupApply(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'id' => 'required|integer|exists:clock_records,id',
            'approve' => 'required|boolean',
            'remark' => 'nullable|string|max:255',
        ],[
            'id.required' => '打卡记录ID不能为空',
            'id.integer' => '打卡记录ID格式错误',
            'id.exists' => '打卡记录不存在',
            'approve.required' => '审核状态不能为空',
            'approve.boolean' => '审核状态格式错误',
        ]);

        // 获取当前管理员
        $admin = Auth::guard('admin')->user();
        if (!$admin) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN, '请先登录管理系统');
        }
        
        $id = $request->input('id');
        $approve = $request->input('approve');
        $remark = $request->input('remark', '');
        
        // 查找补卡记录
        $record = ClockRecord::find($id);
        
        // 检查是否是补卡申请
        if ($record->status != ClockRecord::STATUS_MAKEUP || $record->apply_status != ClockRecord::APPLY_STATUS_APPLIED) {
            return Respond::error(ErrorCodeEnum::MAKEUP_NOT_NEEDED, '该记录不是待处理的补卡申请');
        }
        
        // 检查是否已经审核过
        if ($record->apply_status == ClockRecord::APPLY_STATUS_COMPLETED) {
            return Respond::error(ErrorCodeEnum::MAKEUP_ALREADY_REVIEWED, '该补卡申请已审核');
        }
        
        // 更新补卡申请状态
        $record->apply_status = $approve ? ClockRecord::APPLY_STATUS_COMPLETED : ClockRecord::APPLY_STATUS_PENDING;
        $record->review_time = Carbon::now();
        $record->review_user_id = $admin->id;
        $record->review_remark = $remark;
        
        $record->save();
        
        return Respond::success(null, $approve ? '补卡申请已通过' : '补卡申请已拒绝');
    }
    
    /**
     * 获取打卡统计数据
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getClockStatistics(Request $request): JsonResponse
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d',
            'department_id' => 'nullable|integer|exists:departments,id',
        ],[
            'start_date.date_format' => '开始日期格式错误',
            'end_date.date_format' => '结束日期格式错误',
            'department_id.integer' => '部门ID格式错误',
            'department_id.exists' => '部门不存在',
        ]);

        if ($validator->fails()) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, $validator->errors()->first());
        }
        
        // 如果未提供日期，则默认查询当前自然周（周一至周日）
        if (!$request->has('start_date') || !$request->has('end_date')) {
            $now = Carbon::now();
            $startDate = $now->copy()->startOfWeek(Carbon::MONDAY);
            $endDate = $now->copy()->endOfWeek(Carbon::SUNDAY);
        } else {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
        }
        
        $departmentId = $request->input('department_id');
        
        // 检查日期范围是否超过31天
        $diffInDays = $endDate->diffInDays($startDate) + 1;
        if ($diffInDays > 31) {
            return Respond::error(ErrorCodeEnum::DATE_RANGE_TOO_LARGE, '查询日期范围不能超过31天');
        }
        
        // 获取每日需要打卡的次数
        $dailyClockCount = config('clock.daily_count', 2);
        
        // 构建用户查询
        $userQuery = User::where('status', User::STATUS_NORMAL);
        
        // 部门筛选
        if ($departmentId) {
            $userQuery->where('department_id', $departmentId);
        }
        
        // 获取用户列表
        $users = $userQuery->get();
        $userIds = $users->pluck('id')->toArray();
        
        // 获取指定日期范围内的打卡记录
        $clockRecords = ClockRecord::whereBetween('clock_time', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->whereIn('user_id', $userIds)
            ->get();
        
        // 总体统计数据初始化
        $normalCount = 0;     // 打卡正常人数
        $abnormalCount = 0;   // 打卡异常人数
        $makeupCount = 0;     // 补卡人数
        
        // 按用户和日期整理打卡数据
        $userClockData = [];
        foreach ($users as $user) {
            $userClockData[$user->id] = [
                'user_id' => $user->id,
                'name' => $user->name,
                'department_id' => $user->department_id,
                'dates' => [],
            ];
        }
        
        // 生成日期列表和初始化每日统计数据
        $dailyStatistics = [];
        $dateList = [];
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateStr = $currentDate->format('Y-m-d');
            $dateList[] = $dateStr;
            
            // 初始化当日统计数据
            $dailyStatistics[$dateStr] = [
                'date' => $dateStr,
                'normal_count' => 0,     // 当天打卡正常人数
                'abnormal_count' => 0,   // 当天打卡异常人数
                'makeup_count' => 0,     // 当天有补卡的人数
                'total_count' => count($users), // 总人数
            ];
            
            // 初始化当日用户数据
            foreach ($userIds as $userId) {
                $userClockData[$userId]['dates'][$dateStr] = [
                    'normal_count' => 0,
                    'makeup_count' => 0,
                ];
            }
            
            $currentDate->addDay();
        }
        
        // 统计每个用户每天的打卡情况
        foreach ($clockRecords as $record) {
            $dateStr = $record->clock_time->format('Y-m-d');
            $userId = $record->user_id;
            
            if ($record->status == ClockRecord::STATUS_NORMAL) {
                $userClockData[$userId]['dates'][$dateStr]['normal_count']++;
            } else if ($record->status == ClockRecord::STATUS_MAKEUP && 
                       $record->apply_status == ClockRecord::APPLY_STATUS_COMPLETED) {
                $userClockData[$userId]['dates'][$dateStr]['makeup_count']++;
            }
        }
        
        // 计算每个用户在每一天的打卡状态
        $userClockStatus = [];
        $hasMakeupInPeriod = []; // 记录整个周期内是否有补卡
        
        foreach ($userClockData as $userId => $userData) {
            $isAbnormalInPeriod = false; // 记录整个周期内是否异常
            $hasMakeupInPeriod[$userId] = false;
            
            foreach ($userData['dates'] as $date => $counts) {
                $totalCount = $counts['normal_count'] + $counts['makeup_count'];
                $isAbnormal = $totalCount < $dailyClockCount;
                
                if ($isAbnormal) {
                    $isAbnormalInPeriod = true;
                    $dailyStatistics[$date]['abnormal_count']++;
                } else {
                    $dailyStatistics[$date]['normal_count']++;
                }
                
                if ($counts['makeup_count'] > 0) {
                    $hasMakeupInPeriod[$userId] = true;
                    $dailyStatistics[$date]['makeup_count']++;
                }
            }
            
            $userClockStatus[$userId] = [
                'is_abnormal' => $isAbnormalInPeriod,
            ];
            
            // 更新总体统计数据
            if ($isAbnormalInPeriod) {
                $abnormalCount++;
            } else {
                $normalCount++;
            }
        }
        
        // 计算整个周期内的补卡人数
        foreach ($hasMakeupInPeriod as $hasMakeup) {
            if ($hasMakeup) {
                $makeupCount++;
            }
        }
        
        return Respond::success([
            'summary' => [
                'normal_count' => $normalCount,      // 整个周期内打卡正常人数
                'abnormal_count' => $abnormalCount,  // 整个周期内打卡异常人数
                'makeup_count' => $makeupCount,      // 整个周期内补卡人数
                'total_count' => count($users),      // 总人数
                'daily_clock_count' => $dailyClockCount, // 每日需打卡次数
            ],
            'daily_statistics' => array_values($dailyStatistics), // 每日统计数据
            'date_range' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $diffInDays,
            ],
            'date_list' => $dateList,
        ]);
    }
    
    /**
     * 获取打卡详细列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getClockDetailList(Request $request): JsonResponse
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'start_date' => 'nullable|date_format:Y-m-d',
            'end_date' => 'nullable|date_format:Y-m-d',
            'department_id' => 'nullable|integer|exists:departments,id',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'start_date.date_format' => '开始日期格式错误',
            'end_date.date_format' => '结束日期格式错误',
            'department_id.integer' => '部门ID格式错误',
            'department_id.exists' => '部门不存在',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        if ($validator->fails()) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, $validator->errors()->first());
        }
        
        // 如果未提供日期，则默认查询当前自然周（周一至周日）
        if (!$request->has('start_date') || !$request->has('end_date')) {
            $now = Carbon::now();
            $startDate = $now->copy()->startOfWeek(Carbon::MONDAY);
            $endDate = $now->copy()->endOfWeek(Carbon::SUNDAY);
        } else {
            $startDate = Carbon::parse($request->input('start_date'));
            $endDate = Carbon::parse($request->input('end_date'));
        }
        
        $departmentId = $request->input('department_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);
        
        // 检查日期范围是否超过31天
        $diffInDays = $endDate->diffInDays($startDate) + 1;
        if ($diffInDays > 31) {
            return Respond::error(ErrorCodeEnum::DATE_RANGE_TOO_LARGE, '查询日期范围不能超过31天');
        }
        
        // 获取每日需要打卡的次数
        $dailyClockCount = config('clock.daily_count', 2);
        
        // 构建用户查询
        $userQuery = User::where('status', User::STATUS_NORMAL)
            ->with('department');
        
        // 部门筛选
        if ($departmentId) {
            $userQuery->where('department_id', $departmentId);
        }
        
        // 分页获取用户
        $users = $userQuery->paginate($perPage, ['*'], 'page', $page);
        $userIds = $users->pluck('id')->toArray();
        
        // 获取指定日期范围内的打卡记录
        $clockRecords = ClockRecord::whereBetween('clock_time', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->whereIn('user_id', $userIds)
            ->get()
            ->groupBy(['user_id', function ($item) {
                return $item->clock_time->format('Y-m-d');
            }]);
        
        // 准备返回数据
        $result = [];
        
        // 生成日期列表
        $dateList = [];
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateList[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }
        
        foreach ($users as $user) {
            $userData = [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'department_id' => $user->department_id,
                'department_name' => $user->department ? $user->department->name : '',
                'daily_records' => [],
            ];
            
            // 遍历日期范围
            foreach ($dateList as $date) {
                $dailyRecord = [
                    'date' => $date,
                    'normal_count' => 0,
                    'makeup_count' => 0,
                    'total_count' => 0,
                    'status' => 'abnormal', // 默认异常
                    'records' => [],
                ];
                
                // 获取用户当日打卡记录
                $records = $clockRecords[$user->id][$date] ?? [];
                
                foreach ($records as $record) {
                    if ($record->status == ClockRecord::STATUS_NORMAL) {
                        $dailyRecord['normal_count']++;
                    } else if ($record->status == ClockRecord::STATUS_MAKEUP && 
                              $record->apply_status == ClockRecord::APPLY_STATUS_COMPLETED) {
                        $dailyRecord['makeup_count']++;
                    }
                    
                    $dailyRecord['records'][] = [
                        'id' => $record->id,
                        'clock_time' => $record->clock_time->toDateTimeString(),
                        'status' => $record->status,
                        'status_text' => $record->status_text,
                        'apply_status' => $record->apply_status,
                        'apply_status_text' => $record->apply_status_text,
                    ];
                }
                
                // 计算总打卡次数
                $dailyRecord['total_count'] = $dailyRecord['normal_count'] + $dailyRecord['makeup_count'];
                
                // 判断当日打卡状态
                if ($dailyRecord['total_count'] >= $dailyClockCount) {
                    $dailyRecord['status'] = 'normal'; // 正常
                }
                
                $userData['daily_records'][] = $dailyRecord;
            }
            
            $result[] = $userData;
        }
        
        return Respond::success([
            'items' => $result,
            'date_list' => $dateList,
            'daily_clock_count' => $dailyClockCount,
            'date_range' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $diffInDays,
            ],
            'pagination' => [
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
            ],
        ]);
    }

    /**
     * 获取打卡统计报表
     * 按日统计人员打卡情况：姓名、部门、日打卡次数、缺卡次数、已补卡次数
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getClockStatisticsReport(Request $request): JsonResponse
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'start_date' => 'required|date_format:Y-m-d',
            'end_date' => 'required|date_format:Y-m-d',
            'department_id' => 'nullable|integer|exists:departments,id',
            'user_id' => 'nullable|integer|exists:users,id',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'start_date.required' => '开始日期不能为空',
            'start_date.date_format' => '开始日期格式错误',
            'end_date.required' => '结束日期不能为空',
            'end_date.date_format' => '结束日期格式错误',
            'department_id.integer' => '部门ID格式错误',
            'department_id.exists' => '部门不存在',
            'user_id.integer' => '用户ID格式错误',
            'user_id.exists' => '用户不存在',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        if ($validator->fails()) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, $validator->errors()->first());
        }

        $startDate = Carbon::parse($request->input('start_date'));
        $endDate = Carbon::parse($request->input('end_date'));
        $departmentId = $request->input('department_id');
        $userId = $request->input('user_id');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        // 检查日期范围是否超过31天
        $diffInDays = $endDate->diffInDays($startDate) + 1;
        if ($diffInDays > 31) {
            return Respond::error(ErrorCodeEnum::DATE_RANGE_TOO_LARGE, '查询日期范围不能超过31天');
        }

        // 获取每日需要打卡的次数
        $dailyClockCount = config('clock.daily_count', 2);

        // 构建用户查询
        $userQuery = User::where('status', User::STATUS_NORMAL)
            ->with('department');

        // 部门筛选
        if ($departmentId) {
            $userQuery->where('department_id', $departmentId);
        }

        // 用户筛选
        if ($userId) {
            $userQuery->where('id', $userId);
        }

        // 分页获取用户
        $users = $userQuery->paginate($perPage, ['*'], 'page', $page);
        $userIds = $users->pluck('id')->toArray();

        // 获取指定日期范围内的打卡记录
        $clockRecords = ClockRecord::whereBetween('clock_time', [$startDate->startOfDay(), $endDate->endOfDay()])
            ->whereIn('user_id', $userIds)
            ->get()
            ->groupBy(['user_id', function ($item) {
                return $item->clock_time->format('Y-m-d');
            }]);

        // 生成日期列表
        $dateList = [];
        $currentDate = clone $startDate;
        while ($currentDate <= $endDate) {
            $dateList[] = $currentDate->format('Y-m-d');
            $currentDate->addDay();
        }

        // 准备返回数据
        $result = [];

        foreach ($users as $user) {
            $userData = [
                'user_id' => $user->id,
                'user_name' => $user->name,
                'true_name' => $user->true_name,
                'department_id' => $user->department_id,
                'department_name' => $user->department ? $user->department->name : '',
                'daily_statistics' => [],
                'summary' => [
                    'total_days' => count($dateList),
                    'normal_days' => 0,      // 正常打卡天数
                    'abnormal_days' => 0,    // 异常打卡天数
                    'total_clock_count' => 0,     // 总打卡次数
                    'total_missing_count' => 0,   // 总缺卡次数
                    'total_makeup_count' => 0,    // 总补卡次数
                ],
            ];

            // 遍历日期范围，统计每日打卡情况
            foreach ($dateList as $date) {
                $dailyRecord = [
                    'date' => $date,
                    'clock_count' => 0,      // 当日打卡次数（正常+补卡）
                    'normal_count' => 0,     // 当日正常打卡次数
                    'makeup_count' => 0,     // 当日补卡次数
                    'missing_count' => 0,    // 当日缺卡次数
                    'required_count' => $dailyClockCount, // 当日应打卡次数
                    'status' => 'normal',    // 当日状态：normal-正常，abnormal-异常
                ];

                // 获取用户当日打卡记录
                $records = $clockRecords[$user->id][$date] ?? [];

                foreach ($records as $record) {
                    if ($record->status == ClockRecord::STATUS_NORMAL) {
                        $dailyRecord['normal_count']++;
                    } else if ($record->status == ClockRecord::STATUS_MAKEUP &&
                              $record->apply_status == ClockRecord::APPLY_STATUS_COMPLETED) {
                        $dailyRecord['makeup_count']++;
                    }
                }

                // 计算总打卡次数和缺卡次数
                $dailyRecord['clock_count'] = $dailyRecord['normal_count'] + $dailyRecord['makeup_count'];
                $dailyRecord['missing_count'] = max(0, $dailyClockCount - $dailyRecord['clock_count']);

                // 判断当日状态
                if ($dailyRecord['clock_count'] < $dailyClockCount) {
                    $dailyRecord['status'] = 'abnormal';
                    $userData['summary']['abnormal_days']++;
                } else {
                    $userData['summary']['normal_days']++;
                }

                // 累计统计数据
                $userData['summary']['total_clock_count'] += $dailyRecord['clock_count'];
                $userData['summary']['total_missing_count'] += $dailyRecord['missing_count'];
                $userData['summary']['total_makeup_count'] += $dailyRecord['makeup_count'];

                $userData['daily_statistics'][] = $dailyRecord;
            }

            $result[] = $userData;
        }

        return Respond::success([
            'items' => $result,
            'date_list' => $dateList,
            'daily_clock_count' => $dailyClockCount,
            'date_range' => [
                'start_date' => $startDate->format('Y-m-d'),
                'end_date' => $endDate->format('Y-m-d'),
                'days' => $diffInDays,
            ],
            'pagination' => [
                'total' => $users->total(),
                'per_page' => $users->perPage(),
                'current_page' => $users->currentPage(),
                'last_page' => $users->lastPage(),
            ],
        ]);
    }

    /**
     * 获取申请状态文本
     *
     * @param int $status
     * @return string
     */
    private function getApplyStatusText(int $status): string
    {
        return match ($status) {
            ClockRecord::APPLY_STATUS_NONE => '无需补卡',
            ClockRecord::APPLY_STATUS_PENDING => '待处理',
            ClockRecord::APPLY_STATUS_APPLIED => '已申请',
            ClockRecord::APPLY_STATUS_COMPLETED => '已补卡',
            default => '未知状态',
        };
    }
}
