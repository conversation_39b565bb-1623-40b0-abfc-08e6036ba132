<?php

namespace App\Models;

/**
 * @mixin IdeHelperAdminUser
 */
class AdminUser extends User
{
    protected $table = 'users';

    protected $guard = 'admin';

    /**
     * 获取JWT自定义声明
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [
            'guard' => 'admin',
        ];
    }
    
    /**
     * 重写查询构造器，只查询管理员用户
     *
     * @param $query
     * @return mixed
     */
    public function scopeOnlyAdmin($query)
    {
        return $query->where('is_admin', true)
                     ->where('status', self::STATUS_NORMAL);
    }
}
