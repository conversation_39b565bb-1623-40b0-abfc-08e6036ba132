<?php

namespace App\Services;

use Carbon\Carbon;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class HolidayService
{
    /**
     * 判断指定日期是否为节假日
     *
     * @param Carbon $date
     * @return bool
     */
    public function isHoliday(Carbon $date): bool
    {
        // 检查是否为周末
        if ($date->isWeekend()) {
            return true;
        }
        
        // 检查是否为法定节假日
        return $this->isLegalHoliday($date);
    }
    
    /**
     * 判断是否为法定节假日
     *
     * @param Carbon $date
     * @return bool
     */
    public function isLegalHoliday(Carbon $date): bool
    {
        $dateString = $date->format('Y-m-d');
        
        // 先从缓存中获取
        $cacheKey = "holiday_status_{$dateString}";
        $cached = Cache::get($cacheKey);
        
        if ($cached !== null) {
            return $cached;
        }
        
        // 检查固定日期的法定节假日
        if ($this->isFixedHoliday($date)) {
            Cache::put($cacheKey, true, now()->addDays(30));
            return true;
        }
        
        // 检查农历节假日
        if ($this->isLunarHoliday($date)) {
            Cache::put($cacheKey, true, now()->addDays(30));
            return true;
        }
        
        // 尝试从第三方API获取（可选）
        $isHoliday = $this->checkHolidayFromApi($date);
        Cache::put($cacheKey, $isHoliday, now()->addDays(30));
        
        return $isHoliday;
    }
    
    /**
     * 检查固定日期的法定节假日
     *
     * @param Carbon $date
     * @return bool
     */
    private function isFixedHoliday(Carbon $date): bool
    {
        $monthDay = $date->format('m-d');
        
        // 固定日期的法定节假日
        $fixedHolidays = [
            '01-01', // 元旦
            '05-01', // 劳动节
            '10-01', // 国庆节
            '10-02', // 国庆节
            '10-03', // 国庆节
        ];
        
        return in_array($monthDay, $fixedHolidays);
    }
    
    /**
     * 检查农历节假日
     *
     * @param Carbon $date
     * @return bool
     */
    private function isLunarHoliday(Carbon $date): bool
    {
        $year = $date->year;
        $dateString = $date->format('Y-m-d');
        
        // 获取指定年份的农历节假日
        $lunarHolidays = $this->getLunarHolidays($year);
        
        return in_array($dateString, $lunarHolidays);
    }
    
    /**
     * 获取指定年份的农历节假日
     *
     * @param int $year
     * @return array
     */
    private function getLunarHolidays(int $year): array
    {
        // 这里可以维护一个节假日数据表或接入第三方API
        // 目前返回一些常见的节假日日期作为示例
        
        $holidays = [];
        
        // 根据年份返回对应的农历节假日
        switch ($year) {
            case 2024:
                $holidays = [
                    // 春节假期
                    '2024-02-10', '2024-02-11', '2024-02-12', '2024-02-13',
                    '2024-02-14', '2024-02-15', '2024-02-16', '2024-02-17',
                    // 清明节
                    '2024-04-04', '2024-04-05', '2024-04-06',
                    // 端午节
                    '2024-06-10',
                    // 中秋节
                    '2024-09-15', '2024-09-16', '2024-09-17',
                ];
                break;
            case 2025:
                $holidays = [
                    // 春节假期
                    '2025-01-28', '2025-01-29', '2025-01-30', '2025-01-31',
                    '2025-02-01', '2025-02-02', '2025-02-03',
                    // 清明节
                    '2025-04-05', '2025-04-06', '2025-04-07',
                    // 端午节
                    '2025-05-31', '2025-06-02',
                    // 中秋节
                    '2025-10-06', '2025-10-07', '2025-10-08',
                ];
                break;
            default:
                // 对于其他年份，可以调用第三方API或查询数据库
                break;
        }
        
        return $holidays;
    }
    
    /**
     * 从第三方API检查节假日（可选）
     *
     * @param Carbon $date
     * @return bool
     */
    private function checkHolidayFromApi(Carbon $date): bool
    {
        try {
            // 这里可以接入第三方节假日API
            // 例如：http://timor.tech/api/holiday/info/{date}
            
            $dateString = $date->format('Y-m-d');
            
            // 示例API调用（需要根据实际API调整）
            // $response = Http::timeout(5)->get("http://timor.tech/api/holiday/info/{$dateString}");
            
            // if ($response->successful()) {
            //     $data = $response->json();
            //     return $data['holiday'] ?? false;
            // }
            
            return false;
        } catch (\Exception $e) {
            Log::warning("检查节假日API失败: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * 获取指定日期范围内的所有节假日
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function getHolidaysInRange(Carbon $startDate, Carbon $endDate): array
    {
        $holidays = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate <= $endDate) {
            if ($this->isHoliday($currentDate)) {
                $holidays[] = $currentDate->format('Y-m-d');
            }
            $currentDate->addDay();
        }
        
        return $holidays;
    }
    
    /**
     * 获取指定日期范围内的工作日
     *
     * @param Carbon $startDate
     * @param Carbon $endDate
     * @return array
     */
    public function getWorkdaysInRange(Carbon $startDate, Carbon $endDate): array
    {
        $workdays = [];
        $currentDate = $startDate->copy();
        
        while ($currentDate <= $endDate) {
            if (!$this->isHoliday($currentDate)) {
                $workdays[] = $currentDate->format('Y-m-d');
            }
            $currentDate->addDay();
        }
        
        return $workdays;
    }
    
    /**
     * 清除节假日缓存
     *
     * @param Carbon|null $date 指定日期，为null时清除所有缓存
     * @return void
     */
    public function clearHolidayCache(Carbon $date = null): void
    {
        if ($date) {
            $cacheKey = "holiday_status_{$date->format('Y-m-d')}";
            Cache::forget($cacheKey);
        } else {
            // 清除所有节假日缓存（需要根据实际缓存策略调整）
            Cache::flush();
        }
    }
}
