<?php
namespace App\Providers;

use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\Request; // 引入 Request

// Tymon JWT Auth 相关类
use Tymon\JWTAuth\JWT;
use <PERSON><PERSON>\JWTAuth\Manager;
use Tymon\JWTAuth\JWTGuard;
use Tymon\JWTAuth\Contracts\Providers\Auth as AuthContract; // 用于 UserProvider
use Tymon\JWTAuth\Contracts\Providers\Storage as StorageContract; // 用于 Blacklist
use Tymon\JWTAuth\Contracts\Providers\JWT as JWTProviderContract;
use <PERSON><PERSON>\JWTAuth\Validators\PayloadValidator;
use Ty<PERSON>\JWTAuth\Claims\Factory as ClaimFactory;
use Tymon\JWTAuth\PayloadFactory;
use Tymon\JWTAuth\Blacklist;
use Tymon\JWTAuth\Http\Parser\Parser; // 引入 Parser


class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        // 'App\Models\Model' => 'App\Policies\ModelPolicy',
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        // 注册用于 'api' guard 的 JWT 驱动
        Auth::extend('jwt-api', function ($app, $name, array $config) {
            $guardConfig = config('jwt.guard_configs.api');
            if (!$guardConfig || empty($guardConfig['secret'])) {
                throw new \InvalidArgumentException("JWT configuration for 'api' guard is missing or secret is not set.");
            }

            $jwtInstance = $this->createJwtInstance($app, $guardConfig);
            
            // 创建 UserProvider
            $userProvider = $app['auth']->createUserProvider($config['provider']);
            if (!$userProvider) {
                 throw new \InvalidArgumentException("User provider [{$config['provider']}] is not defined.");
            }

            return new JWTGuard(
                $jwtInstance,
                $userProvider,
                $app['request']
            );
        });

        // 注册用于 'admin' guard 的 JWT 驱动
        Auth::extend('jwt-admin', function ($app, $name, array $config) {
            $guardConfig = config('jwt.guard_configs.admin');
            if (!$guardConfig || empty($guardConfig['secret'])) {
                throw new \InvalidArgumentException("JWT configuration for 'admin' guard is missing or secret is not set.");
            }

            $jwtInstance = $this->createJwtInstance($app, $guardConfig);

            // 创建 UserProvider
            $userProvider = $app['auth']->createUserProvider($config['provider']);
            if (!$userProvider) {
                 throw new \InvalidArgumentException("User provider [{$config['provider']}] is not defined.");
            }

            return new JWTGuard(
                $jwtInstance,
                $userProvider,
                $app['request']
            );
        });
    }

    /**
     * 辅助方法：为给定的配置创建独立的 JWT 实例.
     *
     * @param  \Illuminate\Contracts\Foundation\Application $app
     * @param  array $guardConfig (包含 secret, algo, ttl, provider_class)
     * @return \Tymon\JWTAuth\JWT
     */
    protected function createJwtInstance($app, array $guardConfig)
    {
        // 1. 创建 JWT Provider (例如 Lcobucci)
        $providerClass = $guardConfig['provider_class'] ?? \Tymon\JWTAuth\Providers\JWT\Lcobucci::class;

        /** @var \Tymon\JWTAuth\Contracts\Providers\JWT $jwtProvider */
        $jwtProvider = new $providerClass(
            $guardConfig['secret'],
            $guardConfig['algo'],
            [] // keys: 用于非对称加密，HS256不需要
        );

        // 2. 创建 Manager
        // 我们需要从容器中获取 Blacklist 和 PayloadFactory 的共享实例或配置好的实例
        // tymon/jwt-auth 默认会将这些注册为服务
        /** @var \Tymon\JWTAuth\Blacklist $blacklist */
        $blacklist = $app['tymon.jwt.blacklist'];
        /** @var \Tymon\JWTAuth\Factory $payloadFactory */
        $payloadFactory = $app['tymon.jwt.payload.factory'];
        
        // 手动调整 PayloadFactory 的 TTL，如果需要的话
        // PayloadFactory 内部会使用 ClaimFactory, ClaimFactory 会设置 'ttl' claim
        // $claimFactory = $payloadFactory->getClaimFactory();
        // $claimFactory->setTTL($guardConfig['ttl']); // Tymon v1.0.0 PayloadFactory 没有直接 setTTL，而是通过 ClaimFactory
        // 或者在生成 token 时通过 $jwt->setTTL($ttl)

        $manager = new Manager($jwtProvider, $blacklist, $payloadFactory);
        // 为这个 Manager 实例设置特定的 TTL (这将影响通过此 Manager 生成的 Token)
        $manager->getPayloadFactory()->setTTL($guardConfig['ttl']);


        // 3. 创建 JWT 实例
        // Parser 也从容器获取
        /** @var \Tymon\JWTAuth\Http\Parser\Parser $parser */
        $parser = $app['tymon.jwt.parser'];
        $jwt = new JWT($manager, $parser);

        // 可选: 如果想让 JWT 实例的 setTTL 方法生效，需要确保 Manager 的 PayloadFactory 也被影响
        // $jwt->setTTL($guardConfig['ttl']); // JWT::setTTL 会传递给 Manager

        return $jwt;
    }
}