<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 每日打卡次数
    |--------------------------------------------------------------------------
    |
    | 此配置项定义每人每日需要打卡的次数，默认为2次
    |
    */
    'daily_count' => env('DAILY_CLOCK_COUNT', 2),

    /*
    |--------------------------------------------------------------------------
    | 连续打卡时间间隔
    |--------------------------------------------------------------------------
    |
    | 此配置项定义连续打卡的时间间隔，默认为1小时
    |
    */
    'continuous_clock_interval' => env('CONTINUOUS_CLOCK_INTERVAL', 1),

    /*
    |--------------------------------------------------------------------------
    | 是否排除节假日
    |--------------------------------------------------------------------------
    |
    | 此配置项定义是否排除节假日，默认为true
    |
    */
    'exclude_holiday' => env('EXCLUDE_HOLIDAY', true),

    /*
    |--------------------------------------------------------------------------
    | 打卡时间范围
    |--------------------------------------------------------------------------
    |
    | 此配置项定义打卡的有效时间范围
    |
    */
    'morning_start' => env('CLOCK_MORNING_START', '00:00'),
    'morning_end' => env('CLOCK_MORNING_END', '00:00'),
    'afternoon_start' => env('CLOCK_AFTERNOON_START', '00:00'),
    'afternoon_end' => env('CLOCK_AFTERNOON_END', '00:00'),
];
