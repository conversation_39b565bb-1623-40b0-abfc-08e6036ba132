<?php

use App\Http\AdminControllers\AuthController;
use App\Http\AdminControllers\ClockManageController;
use App\Http\AdminControllers\DepartmentManageController;
use App\Http\AdminControllers\PermissionController;
use App\Http\AdminControllers\RoleController;
use App\Http\AdminControllers\SyncController;
use App\Http\AdminControllers\UserManageController;
use Illuminate\Support\Facades\Route;

// 后台认证路由（不需要认证）
Route::prefix('auth')->group(function () {
    Route::post('login', [AuthController::class, 'login']);
});

// 后台认证路由（需要认证）
Route::prefix('auth')->middleware(['auth:admin', 'admin'])->group(function () {
    Route::get('me', [AuthController::class, 'me']);
    Route::delete('logout', [AuthController::class, 'logout']);
    Route::put('refresh', [AuthController::class, 'refresh']);
});

// 管理端API路由组 - 打卡管理
Route::prefix('clock')->middleware(['auth:admin', 'admin'])->group(function () {
    // 获取打卡记录
    Route::get('records', [ClockManageController::class, 'getClockRecords'])->middleware('role:查看打卡记录|超级管理员');
    // 审核补卡申请
    Route::post('makeup/review', [ClockManageController::class, 'reviewMakeupApply'])->middleware('role:审核补卡申请|超级管理员');
    // 获取打卡统计数据
    Route::get('statistics', [ClockManageController::class, 'getClockStatistics'])->middleware('role:查看打卡记录|超级管理员');
    // 获取打卡详细列表
    Route::get('detail-list', [ClockManageController::class, 'getClockDetailList'])->middleware('role:查看打卡记录|超级管理员');
});

// 管理端API路由组 - 用户管理
Route::prefix('users')->middleware(['auth:admin', 'admin'])->group(function () {
    // 获取用户列表
    Route::get('/', [UserManageController::class, 'getUserList']);
    // 获取用户详情
    Route::get('/{id}', [UserManageController::class, 'getUserDetail']);
    // 更新用户状态
    Route::put('/{id}/status', [UserManageController::class, 'updateUserStatus'])->middleware('role:修改用户状态|超级管理员');
    // 设置管理员权限
    Route::put('/{id}/admin', [UserManageController::class, 'setAdminPermission'])->middleware('role:设置管理员|超级管理员');
    // 为用户分配角色
    Route::post('/{id}/roles', [UserManageController::class, 'assignRoles'])->middleware('role:编辑角色|超级管理员');
    // 获取用户的角色
    Route::get('/{id}/roles', [UserManageController::class, 'getUserRoles'])->middleware('role:查看角色|超级管理员');
    // 获取所有可用角色
    // Route::get('/roles/all', [UserManageController::class, 'getAllRoles']);
});

// 管理端API路由组 - 部门管理
Route::prefix('departments')->middleware(['auth:admin', 'admin'])->group(function () {
    // 获取部门列表
    Route::get('/', [DepartmentManageController::class, 'getDepartmentList']);
    // 获取部门详情
    Route::get('/{id}', [DepartmentManageController::class, 'getDepartmentDetail']);
    // 添加部门
    // Route::post('/', [DepartmentManageController::class, 'addDepartment'])->middleware('role:创建部门');
    // 更新部门
    // Route::put('/{id}', [DepartmentManageController::class, 'updateDepartment'])->middleware('role:编辑部门');
    // 删除部门
    // Route::delete('/{id}', [DepartmentManageController::class, 'deleteDepartment'])->middleware('role:删除部门');
});

// 管理端API路由组 - 角色管理
Route::prefix('roles')->middleware(['auth:admin', 'admin', 'role:查看角色|超级管理员'])->group(function () {
    // 获取角色列表
    Route::get('/', [RoleController::class, 'getRoles']);
    // 获取角色详情
    Route::get('/{id}', [RoleController::class, 'getRoleDetail']);
    // 创建角色
    Route::post('/', [RoleController::class, 'createRole'])->middleware('role:创建角色|超级管理员');
    // 更新角色
    Route::put('/{id}', [RoleController::class, 'updateRole'])->middleware('role:编辑角色|超级管理员');
    // 删除角色
    Route::delete('/{id}', [RoleController::class, 'deleteRole'])->middleware('role:删除角色|超级管理员');
    // 为用户分配角色
    Route::post('/assign', [RoleController::class, 'assignRoleToUser'])->middleware('role:编辑角色|超级管理员');
    // 获取用户的角色
    Route::get('/user/{userId}', [RoleController::class, 'getUserRoles']);
});

// 管理端API路由组 - 权限管理
Route::prefix('permissions')->middleware(['auth:admin', 'admin', 'role:查看权限|超级管理员'])->group(function () {
    // 获取权限列表
    Route::get('/', [PermissionController::class, 'getPermissions']);
    // 获取权限详情
    Route::get('/{id}', [PermissionController::class, 'getPermissionDetail']);
    // 创建权限
    Route::post('/', [PermissionController::class, 'createPermission'])->middleware('role:分配权限|超级管理员');
    // 更新权限
    Route::put('/{id}', [PermissionController::class, 'updatePermission'])->middleware('role:分配权限|超级管理员');
    // 删除权限
    Route::delete('/{id}', [PermissionController::class, 'deletePermission'])->middleware('role:分配权限|超级管理员');
});

// 管理端API路由组 - 同步管理
Route::prefix('sync')->middleware(['auth:admin', 'admin'])->group(function () {
    // 同步部门
    Route::post('department', [SyncController::class, 'syncDepartment']);
    // 同步用户
    Route::post('user', [SyncController::class, 'syncUser']);
});