<?php

namespace App\Providers;

use Illuminate\Support\ServiceProvider;
use <PERSON>vel\Octane\Octane;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        // 配置Octane
        Octane::prepareApplicationForNextOperation(function ($app) {
            // 在每个操作之前准备应用程序
        });

        Octane::prepareApplicationForNextRequest(function ($app) {
            // 在每个请求之前准备应用程序
        });
    }
}
