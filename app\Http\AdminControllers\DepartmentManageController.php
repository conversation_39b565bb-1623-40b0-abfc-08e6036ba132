<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\Department;
use App\Models\User;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class DepartmentManageController extends Controller
{
    /**
     * 获取部门列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getDepartmentList(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request,[
            'keyword' => 'nullable|string|max:50',
            'parent_id' => 'nullable|integer',
        ],[
            'keyword.string' => '关键词格式错误',
            'keyword.max' => '关键词长度不能超过50个字符',
            'parent_id.integer' => '父级部门ID格式错误',
        ]);

        // 获取查询参数
        $keyword = $request->input('keyword');
        $parentId = $request->input('parent_id');

        // 构建查询
        $query = Department::query();

        // 按关键词搜索
        if ($keyword) {
            $query->where(function ($q) use ($keyword) {
                $q->where('name', 'like', "%{$keyword}%")
                  ->orWhere('code', 'like', "%{$keyword}%");
            });
        }

        // 按父级部门筛选
        if ($parentId !== null) {
            $query->where('parent_id', $parentId);
        }

        // 排序
        $query->orderBy('level', 'asc')
              ->orderBy('id', 'asc');

        // 获取所有部门
        $departments = $query->paginate();

        // 处理返回数据
        // $result = [];
        // foreach ($departments as $department) {
        //     // 获取部门下的用户数量
        //     $userCount = User::where('department_id', $department->id)->count();

        //     $result[] = [
        //         'id' => $department->id,
        //         'name' => $department->name,
        //         'code' => $department->code,
        //         'parent_id' => $department->parent_id,
        //         'level' => $department->level,
        //         'user_count' => $userCount,
        //         'created_at' => $department->created_at->toDateTimeString(),
        //     ];
        // }

        return Respond::success($departments);
    }

    /**
     * 获取部门详情
     *
     * @param Request $request
     * @param  $id
     * @return JsonResponse
     */
    public function getDepartmentDetail(Request $request, $id): JsonResponse
    {
        $department = Department::find($id);

        if (!$department) {
            return Respond::error(ErrorCodeEnum::DEPARTMENT_NOT_FOUND);
        }

        // 获取部门下的用户数量
        $userCount = User::where('department_id', $department->id)->count();

        // 获取父级部门
        $parentDepartment = null;
        if ($department->parent_id) {
            $parentDepartment = Department::find($department->parent_id);
        }

        $result = [
            'id' => $department->id,
            'name' => $department->name,
            'code' => $department->code,
            'parent_id' => $department->parent_id,
            'parent_name' => $parentDepartment ? $parentDepartment->name : null,
            'level' => $department->level,
            'user_count' => $userCount,
            'created_at' => $department->created_at->toDateTimeString(),
            'updated_at' => $department->updated_at->toDateTimeString(),
        ];

        return Respond::success($result);
    }

    /**
     * 添加部门
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function addDepartment(Request $request): JsonResponse
    {
        // 验证请求参数
        
        $this->validate($request,[
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:departments,code',
            'parent_id' => 'nullable|integer|exists:departments,id',
            // 'level' => 'required|integer|min:0',
        ],[
            'name.required' => '部门名称不能为空',
            'name.string' => '部门名称格式错误',
            'name.max' => '部门名称长度不能超过50个字符',
            'code.required' => '部门编码不能为空',
            'code.string' => '部门编码格式错误',
            'code.max' => '部门编码长度不能超过50个字符',
            'code.unique' => '部门编码已存在',
            'parent_id.integer' => '父级部门ID格式错误',
            'parent_id.exists' => '父级部门不存在',
        ]);

        // 创建部门
        $department = new Department([
            'name' => $request->input('name'),
            'code' => $request->input('code'),
            'parent_id' => $request->input('parent_id'),
            // 'level' => $request->input('level'),
        ]);

        $department->save();

        return Respond::success(['id' => $department->id], '部门添加成功');
    }

    /**
     * 更新部门
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function updateDepartment(Request $request,$id): JsonResponse
    {
        // 验证请求参数
        $department = Department::find($id);

        if(!$department){
            return Respond::error(ErrorCodeEnum::DEPARTMENT_NOT_FOUND);
        }
        $this->validate($request,[
            'name' => 'required|string|max:50',
            'code' => 'required|string|max:50|unique:departments,code,' . $request->input('id'),
            'parent_id' => 'nullable|integer|exists:departments,id',
            // 'level' => 'required|integer|min:0',
        ],[
            'name.required' => '部门名称不能为空',
            'name.string' => '部门名称格式错误',
            'name.max' => '部门名称长度不能超过50个字符',
            'code.required' => '部门编码不能为空',
            'code.string' => '部门编码格式错误',
            'code.max' => '部门编码长度不能超过50个字符',
            'code.unique' => '部门编码已存在',
            'parent_id.integer' => '父级部门ID格式错误',
            'parent_id.exists' => '父级部门不存在',
        ]);
        // 检查父级部门不能是自己
        if ($request->input('parent_id') == $request->input('id')) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, '父级部门不能是自己');
        }

        // 更新部门
        $department->name = $request->input('name');
        $department->code = $request->input('code');
        $department->parent_id = $request->input('parent_id');
        // $department->level = $request->input('level');
        $department->save();

        return Respond::success(null, '部门更新成功');
    }

    /**
     * 删除部门
     *
     * @param Request $request
     * @param  $id
     * @return JsonResponse
     */
    public function deleteDepartment(Request $request, $id): JsonResponse
    {
        // 检查部门是否存在
        $department = Department::find($id);
        if (!$department) {
            return Respond::error(ErrorCodeEnum::DEPARTMENT_NOT_FOUND);
        }

        // 检查是否有子部门
        $childCount = Department::where('parent_id', $id)->count();
        if ($childCount > 0) {
            return Respond::error(ErrorCodeEnum::DEPARTMENT_HAS_CHILDREN, '该部门下有子部门，无法删除');
        }

        // 检查是否有用户
        $userCount = User::where('department_id', $id)->count();
        if ($userCount > 0) {
            return Respond::error(ErrorCodeEnum::DEPARTMENT_HAS_USERS, '该部门下有用户，无法删除');
        }

        // 删除部门
        $department->delete();

        return Respond::success(null, '部门删除成功');
    }
}
