<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('users', function (Blueprint $table) {
            // 添加前台和后台的openid字段
            $table->string('front_openid')->nullable()->after('openid')->comment('前台登录openid');
            $table->string('admin_openid')->nullable()->after('front_openid')->comment('后台登录openid');
            
            // 添加用户状态字段
            $table->tinyInteger('status')->default(1)->after('department_id')->comment('用户状态：0-禁用，1-正常');
            
            // 添加是否可以登录后台的标记
            $table->boolean('is_admin')->default(false)->after('status')->comment('是否可以登录后台');
            
            // 添加用户角色字段
            $table->string('role')->nullable()->after('is_admin')->comment('用户角色');
            
            // 添加用户真实姓名
            $table->string('true_name')->nullable()->after('name')->comment('真实姓名');
            
            // 添加性别字段
            $table->string('gender')->nullable()->after('avatar')->comment('性别');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn([
                'front_openid',
                'admin_openid',
                'status',
                'is_admin',
                'role',
                'true_name',
                'gender'
            ]);
        });
    }
};
