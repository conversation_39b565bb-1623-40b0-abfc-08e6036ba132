<?php

namespace App\Utils;

use App\Enums\ErrorCodeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Http\Resources\Json\ResourceCollection;
use Illuminate\Pagination\AbstractPaginator;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Arr;

class Respond
{
    /**
     * 标准化数据中的null值
     *
     * @param mixed $data 需要处理的数据
     *
     * @return mixed 处理后的数据
     */
    protected static function normalizeNullValues($data) {
        if ($data === null) {
            return '';
        }

        // 确保数据转换为数组格式
        $data = is_array($data) ? $data : json_decode(json_encode($data), true);

        if (!is_array($data)) {
            return $data;
        }

        return array_map(function ($value) {
            if (is_array($value)) {
                return static::normalizeNullValues($value);
            }

            return $value ?? '';
        }, $data);
    }

    /**
     * 格式化分页数据
     *
     * @param array $data 原始数据
     *
     * @return array 格式化后的数据
     */
    protected static function formatPaginationData(array $data): array {
        // 提取分页元数据
        $meta = Arr::get($data, 'meta', []);

        return [
            'items' => Arr::get($data, 'data', []),
            'pagination' => [
                'total' => Arr::get($meta, 'total', 0),
                'per_page' => Arr::get($meta, 'per_page', 15),
                'current_page' => Arr::get($meta, 'current_page', 1),
                'last_page' => Arr::get($meta, 'last_page', 1),
            ],
        ];
    }

    /**
     * 格式化数据源
     *
     * @param mixed $resource 数据源(支持 Model,Collection,JsonResource,ResourceCollection,array,null)
     *
     * @return array 格式化后的数据
     */
    protected static function formatResourceData($resource): array {
        // 处理 null 值
        if ($resource === null) {
            return [];
        }

        // 处理 ResourceCollection
        if ($resource instanceof ResourceCollection || $resource instanceof AbstractPaginator) {
            if ($resource instanceof AbstractPaginator || $resource->resource instanceof AbstractPaginator) {
                return [
                    'items' => $resource->items(),
                    'pagination' => [
                        'total' => $resource->total(),
                        'per_page' => $resource->perPage(),
                        'current_page' => $resource->currentPage(),
                        'last_page' => $resource->lastPage(),
                    ],
                ];
            }

            $data = $resource->resolve(request());
            // 检查是否包含分页数据
            if (isset($data['meta']) && isset($data['data'])) {
                return static::formatPaginationData($data);
            }

            return $data;
        }

        // 处理 JsonResource
        if ($resource instanceof JsonResource) {
            return $resource->resolve(request());
        }

        // 处理 Model
        if ($resource instanceof Model) {
            return $resource->toArray();
        }

        // 处理 Collection
        if ($resource instanceof Collection) {
            return $resource->toArray();
        }

        // 处理数组
        if (is_array($resource)) {
            return $resource;
        }

        // 其他类型转为数组
        return json_decode(json_encode($resource), true) ?? [];
    }

    /**
     * 生成成功响应
     *
     * @param mixed $resource 数据源
     * @param string $msg 成功提示消息
     * @param int $statusCode HTTP状态码
     *
     * @return JsonResponse
     */
    public static function success($resource = null, string $msg = 'success', int $statusCode = 200): JsonResponse {
        $data = static::formatResourceData($resource);

        return response()->json([
            'errcode' => 0,
            'msg' => $msg,
            'data' => static::normalizeNullValues($data),
        ], $statusCode);
    }

    /**
     * 生成错误响应
     *
     * @param mixed $errcode 错误码
     * @param mixed $errmsg 错误提示消息
     * @param mixed $errdata 错误详细数据
     * @param int $statusCode HTTP状态码
     *
     * @return JsonResponse
     */
    public static function error($errcode = 1, $errmsg = null, $errdata = null, int $statusCode = 400): JsonResponse {
        if ($errcode instanceof ErrorCodeEnum) {
            $errcode = $errcode->value;
            $errmsg = $errmsg ?? ErrorCodeEnum::from($errcode)
                                              ->label();
        }

        if (!is_numeric($errcode)) {
            $errmsg = $errcode;
            $errcode = 999999;
        }

        return response()->json([
            'errcode' => $errcode,
            'errmsg' => $errmsg,
            'errdata' => static::normalizeNullValues(static::formatResourceData($errdata)),
        ], $statusCode);
    }
}
