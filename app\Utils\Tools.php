<?php

namespace App\Utils;

use Carbon\Carbon;
use Illuminate\Support\Facades\Request;
use Illuminate\Support\Str;

class Tools
{
    // 定义标准 header 键名常量
    // 应用ID
    public const HEADER_APP_ID = 'CG-App-ID';
    // 客户端类型
    public const HEADER_CLIENT_TYPE = 'CG-Client-Type';
    // 客户端版本
    public const HEADER_CLIENT_VERSION = 'CG-Client-Version';
    // 客户端构建版本
    public const HEADER_CLIENT_BUILD = 'CG-Client-Build';
    // 设备ID
    public const HEADER_DEVICE_ID = 'CG-Device-ID';
    // 设备型号
    public const HEADER_DEVICE_MODEL = 'CG-Device-Model';
    // 设备操作系统
    public const HEADER_DEVICE_OS = 'CG-Device-OS';
    // 设备操作系统版本
    public const HEADER_DEVICE_OS_VERSION = 'CG-Device-OS-Version';
    // 网络类型
    public const HEADER_NETWORK_TYPE = 'CG-Network-Type';
    // 用户ID
    public const HEADER_USER_ID = 'CG-User-ID';
    // 请求ID
    public const HEADER_REQUEST_ID = 'CG-Request-ID';
    // 时间戳
    public const HEADER_TIMESTAMP = 'CG-Timestamp';
    // 时区
    public const HEADER_TIMEZONE = 'CG-Timezone';
    // API版本
    public const HEADER_API_VERSION = 'CG-API-Version';
    // 语言
    public const HEADER_LANGUAGE = 'CG-Language';
    // 地区
    public const HEADER_REGION = 'CG-Region';
    // 渠道
    public const HEADER_CHANNEL = 'CG-Channel';
    // 签名
    public const HEADER_SIGN = 'CG-Signature';
    // 随机字符串
    public const HEADER_NONCE = 'CG-Nonce';
    // 客户端ID
    public const HEADER_CLIENT_ID = 'CG-Client-ID';

    public const SYSTEM_OS_IOS = 'ios';
    public const SYSTEM_OS_IPADOS = 'ipados';
    public const SYSTEM_OS_ANDROID = 'android';
    public const SYSTEM_OS_HARMONYOS = 'harmonyos';
    public const SYSTEM_OS_WEB = 'web';
    public const SYSTEM_OS_PC = 'pc';
    public const SYSTEM_OS_WECHAT = 'wechat';

    /**
     * 获取客户端真实IP地址
     *
     * @return string
     */
    public static function getClientIp(): string {
        $request = Request::instance();

        // 获取代理IP链
        if ($xff = $request->header('X-Forwarded-For')) {
            $ips = explode(',', $xff);

            return trim($ips[0]);
        }

        if ($xRealIp = $request->header('X-Real-IP')) {
            return $xRealIp;
        }

        return $request->ip();
    }

    /**
     * 获取设备ID
     *
     * @return string|null
     */
    public static function getDeviceId(): ?string {
        return self::getHeader(self::HEADER_DEVICE_ID);
    }

    /**
     * 获取操作系统
     *
     * @return string|null
     */
    public static function getOS(): ?string {
        return self::getHeader(self::HEADER_DEVICE_OS);
    }

    /**
     * 获取操作系统版本
     *
     * @return string|null
     */
    public static function getOSVersion(): ?string {
        return self::getHeader(self::HEADER_DEVICE_OS_VERSION);
    }

    /**
     * 获取平台信息 (ios/android/web)
     *
     * @return string|null
     */
    public static function getPlatform(): ?string {
        return self::getHeader(self::HEADER_CLIENT_TYPE);
    }

    /**
     * 获取APP版本号
     *
     * @return string|null
     */
    public static function getAppVersion(): ?string {
        return self::getHeader(self::HEADER_CLIENT_VERSION);
    }

    /**
     * 获取构建版本号
     *
     * @return string|null
     */
    public static function getBuildVersion(): ?string {
        return self::getHeader(self::HEADER_CLIENT_BUILD);
    }

    /**
     * 获取网络类型
     *
     * @return string|null
     */
    public static function getNetworkType(): ?string {
        return self::getHeader(self::HEADER_NETWORK_TYPE);
    }

    /**
     * 获取设备型号
     *
     * @return string|null
     */
    public static function getDeviceModel(): ?string {
        return self::getHeader(self::HEADER_DEVICE_MODEL);
    }

    /**
     * 获取用户ID
     *
     * @return string|null
     */
    public static function getUserId(): ?string {
        return self::getHeader(self::HEADER_USER_ID);
    }

    /**
     * 获取请求ID
     *
     * @return string
     */
    public static function getRequestId(): string {
        return self::getHeader(self::HEADER_REQUEST_ID);
    }

    /**
     * 获取时区信息
     *
     * @return string|null
     */
    public static function getTimezone(): ?string {
        return self::getHeader(self::HEADER_TIMEZONE);
    }

    /**
     * 获取语言设置
     *
     * @return string|null
     */
    public static function getLanguage(): ?string {
        return self::getHeader(self::HEADER_LANGUAGE);
    }

    /**
     * 获取地区设置
     *
     * @return string|null
     */
    public static function getRegion(): ?string {
        return self::getHeader(self::HEADER_REGION);
    }

    /**
     * 获取渠道信息
     *
     * @return string|null
     */
    public static function getChannel(): ?string {
        return self::getHeader(self::HEADER_CHANNEL);
    }

    /**
     * 获取时间戳
     *
     * @return string
     */
    public static function getTimestamp(): ?string {
        return self::getHeader(self::HEADER_TIMESTAMP) ?? Request::input('timestamp');
    }

    /**
     * 获取API版本号
     *
     * @return string
     */
    public static function getApiVersion(): ?string {
        return self::getHeader(self::HEADER_API_VERSION, '');
    }

    /**
     * 获取App ID
     *
     * @return string
     */
    public static function getAppId(): ?string {
        return self::getHeader(self::HEADER_APP_ID, '');
    }

    /**
     * 获取随机字符串
     *
     * @return string
     */
    public static function getNonce(): ?string {
        return self::getHeader(self::HEADER_NONCE) ?? Request::input('nonce');
    }

    /**
     * 获取签名
     *
     * @return string
     */
    public static function getSignature(): ?string {
        return self::getHeader(self::HEADER_SIGN) ?? Request::input('signature');
    }

    /**
     * 获取客户端ID
     *
     * @return string
     */
    public static function getClientId(): ?string {
        return self::getHeader(self::HEADER_CLIENT_ID) ?? Request::input('client_id');
    }

    /**
     * 对比APP版本号
     */
    public static function compareAppVersion(string $version): bool {
        $cleanVersion = ltrim($version, 'v');
        $currentVersion = ltrim(self::getAppVersion(), 'v');

        return version_compare($currentVersion, $cleanVersion, '>=');
    }

    /**
     * 获取所有设备相关信息
     *
     * @return array
     */
    public static function getDeviceInfo(): array {
        return [
            'ip'            => self::getClientIp(),
            'device_id'     => self::getDeviceId(),
            'device_model'  => self::getDeviceModel(),
            'os'            => self::getOS(),
            'os_version'    => self::getOSVersion(),
            'platform'      => self::getPlatform(),
            'app_version'   => self::getAppVersion(),
            'build_version' => self::getBuildVersion(),
            'network_type'  => self::getNetworkType(),
            'user_agent'    => self::getUserAgent(),
            'language'      => self::getLanguage(),
            'region'        => self::getRegion(),
            'timezone'      => self::getTimezone(),
            'channel'       => self::getChannel(),
            'request_id'    => self::getRequestId(),
        ];
    }

    /**
     * 获取指定header参数
     *
     * @param string $key
     * @param mixed $default
     *
     * @return mixed
     */
    public static function getHeader(string $key, $default = null) {
        return Request::header($key, $default);
    }

    /**
     * 获取User-Agent
     *
     * @return string|null
     */
    public static function getUserAgent(): ?string {
        return self::getHeader('User-Agent');
    }

    /**
     * 检查是否是移动设备
     *
     * @return bool
     */
    public static function isMobile(): bool {
        $userAgent = self::getUserAgent();

        return (bool)preg_match('/(android|iphone|harmonyos|ipad|mobile)/i', $userAgent);
    }

    /**
     * 检查是否是 iOS 设备
     *
     * @return bool
     */
    public static function isIOS(): bool {
        return strtolower(self::getOS() ?? '') === self::SYSTEM_OS_IOS || self::isIpadOS();
    }

    /**
     * 检查是否是 iPadOS 设备
     *
     * @return bool
     */
    public static function isIpadOS(): bool {
        return strtolower(self::getOS() ?? '') === self::SYSTEM_OS_IPADOS;
    }

    /**
     * 检查是否是 Android 设备
     *
     * @return bool
     */
    public static function isAndroid(): bool {
        return strtolower(self::getOS() ?? '') === self::SYSTEM_OS_ANDROID;
    }

    /**
     * 检查是否是 HarmonyOS 设备
     *
     * @return bool
     */
    public static function isHarmonyOS(): bool {
        return strtolower(self::getOS() ?? '') === self::SYSTEM_OS_HARMONYOS;
    }

    /**
     * 检查是否是 Web 平台
     *
     * @return bool
     */
    public static function isWeb(): bool {
        return strtolower(self::getPlatform() ?? '') === self::SYSTEM_OS_WEB || self::isPC();
    }

    /**
     * 检查是否是 PC 平台
     * @return bool
     */
    public static function isPC(): bool {
        return strtolower(self::getPlatform() ?? '') === self::SYSTEM_OS_PC;
    }

    /**
     * 检查是否是 wechat 平台
     */
    public static function isWechat(): bool {
        return strtolower(self::getPlatform() ?? '') === self::SYSTEM_OS_WECHAT;
    }

    /**
     * 获取设备类型
     *
     * @return string
     */
    public static function getDeviceType(): string {
        if (self::isIOS()) {
            return self::SYSTEM_OS_IOS;
        }

        //        if (self::isIpadOS()) {
        //            return self::SYSTEM_OS_IPADOS;
        //        }

        if (self::isAndroid()) {
            return self::SYSTEM_OS_ANDROID;
        }

        if (self::isHarmonyOS()) {
            return self::SYSTEM_OS_HARMONYOS;
        }

        if (self::isWeb()) {
            return self::SYSTEM_OS_WEB;
        }

        //        if (self::isPC()) {
        //            return self::SYSTEM_OS_PC;
        //        }

        if (self::isWechat()) {
            return self::SYSTEM_OS_WECHAT;
        }

        return 'unknown';
    }


    /**
     * @param $fieldName
     * @param $dateTime
     *
     * @return array
     */
    public static function mergeDateTimeFormat($fieldName, $dateTime) {
        if (!$dateTime instanceof \DateTime) {
            if (!strtotime($dateTime)) {
                $dateTime = Carbon::createFromTimestamp($dateTime);
            } else {
                $dateTime = Carbon::create($dateTime);
            }
        }

        if ($dateTime->timestamp) {
            return [
                $fieldName . '_timestamp' => $dateTime->timestamp,
                $fieldName . '_gmt'       => $dateTime->toISOString(),
                $fieldName . '_human'     => $dateTime->diffForHumans(),
                $fieldName                => $dateTime->toDateTimeString(),
            ];
        } else {
            return [
                $fieldName . '_timestamp' => 0,
                $fieldName . '_gmt'       => '',
                $fieldName . '_human'     => '',
                $fieldName                => '',
            ];
        }
    }
}
