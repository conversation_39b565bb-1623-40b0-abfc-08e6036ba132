<?php

namespace Tests\Feature;

use App\Enums\ErrorCodeEnum;
use App\Models\ClockRecord;
use App\Models\User;
use App\Services\LocationService;
use Carbon\Carbon;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ClockLocationValidationTest extends TestCase
{
    use RefreshDatabase;

    protected $user;
    protected $locationService;

    protected function setUp(): void
    {
        parent::setUp();
        
        // 创建测试用户
        $this->user = User::factory()->create();
        $this->locationService = new LocationService();
    }

    /**
     * 测试位置验证功能关闭时允许任意位置打卡
     */
    public function test_clock_in_when_location_validation_disabled()
    {
        // 关闭位置验证
        config(['clock.clock_range_enable' => false]);

        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/clock/in', [
                'latitude' => 40.0000,  // 任意位置
                'longitude' => 117.0000,
            ]);

        $response->assertStatus(200)
            ->assertJsonStructure([
                'code',
                'message',
                'data' => [
                    'id',
                    'clock_time',
                    'count'
                ]
            ]);

        // 验证数据库中有打卡记录
        $this->assertDatabaseHas('clock_records', [
            'user_id' => $this->user->id,
            'status' => ClockRecord::STATUS_NORMAL,
            'latitude' => 40.0000,
            'longitude' => 117.0000,
        ]);
    }

    /**
     * 测试在允许范围内打卡成功
     */
    public function test_clock_in_within_allowed_range()
    {
        // 启用位置验证
        config([
            'clock.clock_range_enable' => true,
            'clock.clock_range_center_latitude' => 39.9042,
            'clock.clock_range_center_longitude' => 116.4074,
            'clock.clock_range_distance' => 500, // 500米范围
        ]);

        // 在中心点附近100米处打卡
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/clock/in', [
                'latitude' => 39.9050,  // 距离中心点约100米
                'longitude' => 116.4074,
            ]);

        $response->assertStatus(200);

        // 验证数据库中有打卡记录
        $this->assertDatabaseHas('clock_records', [
            'user_id' => $this->user->id,
            'status' => ClockRecord::STATUS_NORMAL,
        ]);
    }

    /**
     * 测试超出允许范围时打卡失败
     */
    public function test_clock_in_outside_allowed_range()
    {
        // 启用位置验证
        config([
            'clock.clock_range_enable' => true,
            'clock.clock_range_center_latitude' => 39.9042,
            'clock.clock_range_center_longitude' => 116.4074,
            'clock.clock_range_distance' => 500, // 500米范围
        ]);

        // 在距离中心点很远的地方打卡
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/clock/in', [
                'latitude' => 40.0000,  // 距离中心点约10公里
                'longitude' => 116.5000,
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'code' => ErrorCodeEnum::CLOCK_LOCATION_INVALID->value,
            ]);

        // 验证数据库中没有打卡记录
        $this->assertDatabaseMissing('clock_records', [
            'user_id' => $this->user->id,
        ]);
    }

    /**
     * 测试无效坐标时打卡失败
     */
    public function test_clock_in_with_invalid_coordinates()
    {
        // 启用位置验证
        config(['clock.clock_range_enable' => true]);

        // 使用无效的纬度
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/clock/in', [
                'latitude' => 91.0000,  // 无效纬度（超出-90到90范围）
                'longitude' => 116.4074,
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'code' => ErrorCodeEnum::CLOCK_LOCATION_INVALID->value,
                'message' => '打卡位置坐标无效',
            ]);

        // 使用无效的经度
        $response = $this->actingAs($this->user, 'api')
            ->postJson('/api/clock/in', [
                'latitude' => 39.9042,
                'longitude' => 181.0000,  // 无效经度（超出-180到180范围）
            ]);

        $response->assertStatus(400)
            ->assertJson([
                'code' => ErrorCodeEnum::CLOCK_LOCATION_INVALID->value,
                'message' => '打卡位置坐标无效',
            ]);
    }

    /**
     * 测试打卡状态接口返回位置配置信息
     */
    public function test_clock_status_returns_location_config()
    {
        // 启用位置验证
        config([
            'clock.clock_range_enable' => true,
            'clock.clock_range_center_latitude' => 39.9042,
            'clock.clock_range_center_longitude' => 116.4074,
            'clock.clock_range_distance' => 500,
        ]);

        $response = $this->actingAs($this->user, 'api')
            ->getJson('/api/clock/status');

        $response->assertStatus(200)
            ->assertJsonStructure([
                'code',
                'message',
                'data' => [
                    'status',
                    'count',
                    'today_clock_records',
                    'is_valid_time',
                    'current_period',
                    'time_ranges',
                    'location_config' => [
                        'enabled',
                        'center_latitude',
                        'center_longitude',
                        'max_distance',
                    ]
                ]
            ])
            ->assertJson([
                'data' => [
                    'location_config' => [
                        'enabled' => true,
                        'center_latitude' => 39.9042,
                        'center_longitude' => 116.4074,
                        'max_distance' => 500,
                    ]
                ]
            ]);
    }

    /**
     * 测试距离计算功能
     */
    public function test_distance_calculation()
    {
        // 测试北京天安门到故宫的距离（约1公里）
        $distance = $this->locationService->calculateDistance(
            39.9042, 116.4074, // 天安门
            39.9163, 116.3972  // 故宫
        );

        // 距离应该在900-1100米之间
        $this->assertGreaterThan(900, $distance);
        $this->assertLessThan(1100, $distance);
    }

    /**
     * 测试位置验证服务
     */
    public function test_location_validation_service()
    {
        // 设置测试配置
        config([
            'clock.clock_range_enable' => true,
            'clock.clock_range_center_latitude' => 39.9042,
            'clock.clock_range_center_longitude' => 116.4074,
            'clock.clock_range_distance' => 500,
        ]);

        // 测试在范围内的位置
        $result = $this->locationService->validateClockLocation(39.9050, 116.4074);
        $this->assertTrue($result['valid']);
        $this->assertLessThan(500, $result['distance']);

        // 测试超出范围的位置
        $result = $this->locationService->validateClockLocation(40.0000, 116.5000);
        $this->assertFalse($result['valid']);
        $this->assertGreaterThan(500, $result['distance']);
    }

    /**
     * 测试坐标有效性验证
     */
    public function test_coordinate_validation()
    {
        // 有效坐标
        $this->assertTrue($this->locationService->isValidCoordinate(39.9042, 116.4074));
        $this->assertTrue($this->locationService->isValidCoordinate(-90, -180));
        $this->assertTrue($this->locationService->isValidCoordinate(90, 180));

        // 无效坐标
        $this->assertFalse($this->locationService->isValidCoordinate(91, 116.4074));
        $this->assertFalse($this->locationService->isValidCoordinate(-91, 116.4074));
        $this->assertFalse($this->locationService->isValidCoordinate(39.9042, 181));
        $this->assertFalse($this->locationService->isValidCoordinate(39.9042, -181));
    }

    /**
     * 测试距离格式化
     */
    public function test_distance_formatting()
    {
        $this->assertEquals('500米', $this->locationService->formatDistance(500));
        $this->assertEquals('1.5公里', $this->locationService->formatDistance(1500));
        $this->assertEquals('10.25公里', $this->locationService->formatDistance(10250));
    }

    /**
     * 测试方位角计算
     */
    public function test_bearing_calculation()
    {
        // 从北京到上海的方位角应该大致是东南方向
        $bearing = $this->locationService->calculateBearing(
            39.9042, 116.4074, // 北京
            31.2304, 121.4737  // 上海
        );

        $direction = $this->locationService->getBearingDirection($bearing);
        
        // 方位角应该在90-180度之间（东南方向）
        $this->assertGreaterThan(90, $bearing);
        $this->assertLessThan(180, $bearing);
        $this->assertContains($direction, ['东', '东南', '南']);
    }
}
