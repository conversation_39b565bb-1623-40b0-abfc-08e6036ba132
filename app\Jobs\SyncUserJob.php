<?php

namespace App\Jobs;

use App\Models\Department;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class SyncUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 单个用户数据
     *
     * @var array
     */
    protected $userData;

    /**
     * 创建新的任务实例
     *
     * @param array $userData 单个用户数据
     */
    public function __construct(array $userData)
    {
        $this->userData = $userData;
    }

    /**
     * 执行任务
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $departmentId = null;

            // 查询用户所属部门
            if (!empty($this->userData['departments'][0]['code'])) {
                $deptCode = $this->userData['departments'][0]['code'];
                $department = Department::where('code', $deptCode)->first();
                
                if ($department) {
                    $departmentId = $department->id;
                }
                // 不再记录未找到部门的警告日志
            }

            // 更新或创建用户
            User::updateOrCreate(
                ['unionid' => $this->userData['union_id']],
                [
                    'name' => $this->userData['display_name'],
                    'true_name' => $this->userData['true_name'],
                    'password' => bcrypt(Str::uuid()->toString()),
                    'phone' => $this->userData['mobile'],
                    'avatar' => $this->userData['avatar'],
                    'gender' => $this->userData['gender'] ?? null,
                    'department_id' => $departmentId,
                    'status' => User::STATUS_NORMAL,
                ]
            );

            // 不再记录成功同步的日志
        } catch (\Exception $e) {
            // 只记录关键错误信息
            Log::error('用户同步失败', [
                'unionid' => $this->userData['union_id'] ?? '未知',
                'name' => $this->userData['display_name'] ?? '未知',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
} 