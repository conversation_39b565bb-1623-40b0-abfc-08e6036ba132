<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use <PERSON><PERSON>\JWTAuth\Contracts\JWTSubject;
use Spatie\Permission\Traits\HasRoles;

/**
 * @mixin IdeHelperUser
 */
class User extends Authenticatable implements JWTSubject
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable, HasRoles;
    use SoftDeletes;

    /**
     * 用户状态：禁用
     */
    public const STATUS_DISABLED = 0;

    /**
     * 用户状态：正常
     */
    public const STATUS_NORMAL = 1;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'true_name',
        'password',
        'phone',
        'avatar',
        'gender',
        'openid',
        'unionid',
        'front_openid',
        'admin_openid',
        'department_id',
        'status',
        'is_admin',
        'role',
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            // 'status' => 'integer',
            // 'is_admin' => 'boolean',
            // 'password' => 'hashed',
        ];
    }

    /**
     * 获取JWT标识符
     *
     * @return mixed
     */
    public function getJWTIdentifier()
    {
        return $this->getKey();
    }

    /**
     * 获取JWT自定义声明
     *
     * @return array
     */
    public function getJWTCustomClaims()
    {
        return [
            'guard' => 'api',
        ];
    }

    /**
     * 检查用户是否可以登录后台
     *
     * @return bool
     */
    public function canLoginAdmin(): bool
    {
        return $this->is_admin && $this->status === self::STATUS_NORMAL;
    }

    /**
     * 检查用户是否处于正常状态
     *
     * @return bool
     */
    public function isActive(): bool
    {
        return $this->status === self::STATUS_NORMAL;
    }

    /**
     * 获取用户所属部门
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function department()
    {
        return $this->belongsTo(Department::class);
    }
}
