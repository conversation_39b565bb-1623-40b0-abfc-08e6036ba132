<?php

namespace App\Console\Commands;

use App\Enums\ErrorCodeEnum;
use App\Jobs\SyncDepartmentJob;
use App\Jobs\SyncUserJob;
use App\Utils\Respond;
use Changguan\LaravelSDK\Facades\Changguan;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SyncContactsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:contacts 
                            {type? : The type of sync to perform (department, user or all)} 
                            {--lesslog : 以静默模式执行，减少输出}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '同步通讯录数据';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type') ?? 'all';
        $isQuiet = $this->option('lesslog');

        if ($type === 'all' || $type === 'department') {
            $this->syncDepartment($isQuiet);
        }

        if ($type === 'all' || $type === 'user') {
            $this->syncUser($isQuiet);
        }

        if (!$isQuiet) {
            $this->info('所有同步任务已分发完成');
        }
        
        return 0;
    }

    /**
     * 获取部门数据并分发同步任务
     * 
     * @param bool $isQuiet 是否以静默模式执行
     */
    private function syncDepartment(bool $isQuiet = false)
    {
        if (!$isQuiet) {
            $this->info('获取部门数据...');
        }
        
        $response = Changguan::oauth()->getEnterpriseDepartmentList();

        if ($response['errcode'] !== 0) {
            $errorMessage = '获取部门列表失败: ' . ($response['msg'] ?? '未知错误');
            
            if (!$isQuiet) {
                $this->error($errorMessage);
            }
            
            Log::error($errorMessage);
            return;
        }

        $departments = $response['data'];
        $totalDepartments = count($departments);
        
        if (!$isQuiet) {
            $this->info("获取到 {$totalDepartments} 个部门，开始分发同步任务");
            
            // 创建进度条
            $bar = $this->output->createProgressBar($totalDepartments);
            $bar->start();
        }
        
        // 为每个部门分发单独的同步任务
        foreach ($departments as $department) {
            SyncDepartmentJob::dispatch($department)->onQueue('default');
            
            if (!$isQuiet && isset($bar)) {
                $bar->advance();
            }
        }

        if (!$isQuiet && isset($bar)) {
            $bar->finish();
            $this->newLine();
            $this->info('部门同步任务已全部分发');
        }
        
        // 只记录简洁的概要信息
        Log::info("部门同步开始，已分发 {$totalDepartments} 个任务");
    }

    /**
     * 获取用户数据并分发同步任务
     * 
     * @param bool $isQuiet 是否以静默模式执行
     */
    private function syncUser(bool $isQuiet = false)
    {
        if (!$isQuiet) {
            $this->info('获取用户数据...');
        }
        
        $response = Changguan::oauth()->getEnterpriseFullContacts();

        if ($response['errcode'] !== 0) {
            $errorMessage = '获取用户列表失败: ' . ($response['msg'] ?? '未知错误');
            
            if (!$isQuiet) {
                $this->error($errorMessage);
            }
            
            Log::error($errorMessage);
            return;
        }

        $users = $response['data'];
        $totalUsers = count($users);

        if (empty($users)) {
            if (!$isQuiet) {
                $this->info('没有用户需要同步');
            }
            
            // 这种情况下不需要记录日志
            return;
        }

        if (!$isQuiet) {
            $this->info("获取到 {$totalUsers} 个用户，开始分发同步任务");

            // 创建进度条
            $bar = $this->output->createProgressBar($totalUsers);
            $bar->start();
        }
        
        // 为每个用户分发单独的同步任务
        foreach ($users as $user) {
            SyncUserJob::dispatch($user)->onQueue('default');
            
            if (!$isQuiet && isset($bar)) {
                $bar->advance();
            }
        }

        if (!$isQuiet && isset($bar)) {
            $bar->finish();
            $this->newLine();
            $this->info('用户同步任务已全部分发');
            $this->warn('请确保有足够的队列处理器在运行：');
            $this->line('php artisan queue:work --queue=sync_users');
        }
        
        // 只记录简洁的概要信息
        Log::info("用户同步开始，已分发 {$totalUsers} 个任务");
    }
}
