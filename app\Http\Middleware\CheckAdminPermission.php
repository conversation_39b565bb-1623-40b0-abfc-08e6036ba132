<?php

namespace App\Http\Middleware;

use App\Enums\ErrorCodeEnum;
use App\Utils\Respond;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class CheckAdminPermission
{
    /**
     * 处理传入的请求
     *
     * @param Request $request
     * @param Closure $next
     * @param string ...$permissions
     * @return mixed
     */
    public function handle(Request $request, Closure $next, ...$permissions)
    {
        // 检查用户是否已认证
        if (!Auth::guard('admin')->check()) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN, '请先登录');
        }

        // 获取当前用户
        $user = Auth::guard('admin')->user();

        // 检查用户是否有权限访问后台
        if (!$user->canLoginAdmin()) {
            return Respond::error(ErrorCodeEnum::USER_STATUS_ERROR, '您没有权限访问后台');
        }

        // 如果指定了权限，检查用户是否有权限
        if (!empty($permissions)) {
            // 检查用户是否有任一权限
            $hasPermission = false;
            foreach ($permissions as $permission) {
                if ($user->hasPermissionTo($permission, 'admin')) {
                    $hasPermission = true;
                    break;
                }
            }

            if (!$hasPermission) {
                return Respond::error(ErrorCodeEnum::USER_STATUS_ERROR, '您没有权限执行此操作');
            }
        }

        return $next($request);
    }
}
