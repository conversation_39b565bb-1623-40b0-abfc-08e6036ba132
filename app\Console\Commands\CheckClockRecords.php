<?php

namespace App\Console\Commands;

use App\Models\ClockRecord;
use App\Models\User;
use App\Services\HolidayService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CheckClockRecords extends Command
{
    /**
     * 命令名称
     *
     * @var string
     */
    protected $signature = 'clock:check {date? : 要检查的日期，格式为Y-m-d，默认为昨天}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '检查用户打卡记录，为未完成打卡的用户创建未打卡记录';

    /**
     * 执行命令
     */
    public function handle()
    {
        // 获取要检查的日期，默认为昨天
        $date = $this->argument('date') ? Carbon::parse($this->argument('date')) : Carbon::yesterday();
        $dateString = $date->toDateString();

        $this->info("开始检查 {$dateString} 的打卡记录");

        // 检查是否需要排除节假日
        $excludeHoliday = config('clock.exclude_holiday', true);

        if ($excludeHoliday) {
            $holidayService = new HolidayService();
            if ($holidayService->isHoliday($date)) {
                $this->info("检测到 {$dateString} 为节假日，跳过打卡记录检查");
                return 0;
            }
        }

        // 获取每日需要打卡的次数
        $dailyClockCount = config('clock.daily_count', 2);
        
        // 获取所有用户
        $users = User::all();
        $totalUsers = $users->count();
        $processedUsers = 0;
        $incompleteUsers = 0;
        
        $this->info("共有 {$totalUsers} 个用户需要检查");
        
        // 创建进度条
        $bar = $this->output->createProgressBar($totalUsers);
        $bar->start();
        
        foreach ($users as $user) {
            // 获取用户当天的打卡记录数
            $clockCount = ClockRecord::ofUser($user->id)
                ->whereDate('clock_time', $dateString)
                ->count();
            
            // 如果打卡次数不足
            if ($clockCount < $dailyClockCount) {
                $incompleteUsers++;
                $missingCount = $dailyClockCount - $clockCount;
                
                // 开始事务
                DB::beginTransaction();
                
                try {
                    // 为用户创建未打卡记录
                    for ($i = 0; $i < $missingCount; $i++) {
                        $clockRecord = new ClockRecord([
                            'user_id' => $user->id,
                            'clock_time' => Carbon::parse($dateString)->addHours(9 + $i), // 默认上午9点开始，每小时一次
                            'status' => ClockRecord::STATUS_MAKEUP,
                            'apply_status' => ClockRecord::APPLY_STATUS_PENDING,
                        ]);
                        
                        $clockRecord->save();
                    }
                    
                    DB::commit();
                } catch (\Exception $e) {
                    DB::rollBack();
                    Log::error("为用户 {$user->id} 创建未打卡记录失败: " . $e->getMessage());
                    $this->error("为用户 {$user->id} 创建未打卡记录失败: " . $e->getMessage());
                }
            }
            
            $processedUsers++;
            $bar->advance();
        }
        
        $bar->finish();
        $this->newLine();
        
        $this->info("检查完成，共处理 {$processedUsers} 个用户，其中 {$incompleteUsers} 个用户打卡不足");

        return 0;
    }

}
