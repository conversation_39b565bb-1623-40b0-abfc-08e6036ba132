# 打卡统计报表接口文档

## 接口概述

新增的打卡统计报表接口用于按日统计时间范围内人员的打卡情况，支持按部门和人员查询。

## 接口信息

- **URL**: `/admin/api/clock/statistics-report`
- **方法**: `GET`
- **权限**: 需要管理员权限和"查看打卡记录"或"超级管理员"角色

## 请求参数

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| start_date | string | 是 | 开始日期，格式：Y-m-d |
| end_date | string | 是 | 结束日期，格式：Y-m-d |
| department_id | integer | 否 | 部门ID，筛选指定部门的用户 |
| user_id | integer | 否 | 用户ID，筛选指定用户 |
| page | integer | 否 | 页码，默认为1 |
| per_page | integer | 否 | 每页数量，默认为20，最大100 |

## 限制条件

- 时间范围最多31天
- 只查询状态为正常的用户
- 只统计已完成的补卡记录

## 响应示例

```json
{
    "code": 200,
    "message": "success",
    "data": {
        "items": [
            {
                "user_id": 1,
                "user_name": "张三",
                "true_name": "张三",
                "department_id": 1,
                "department_name": "技术部",
                "daily_statistics": [
                    {
                        "date": "2024-01-01",
                        "clock_count": 2,
                        "normal_count": 2,
                        "makeup_count": 0,
                        "missing_count": 0,
                        "required_count": 2,
                        "status": "normal"
                    },
                    {
                        "date": "2024-01-02",
                        "clock_count": 1,
                        "normal_count": 1,
                        "makeup_count": 0,
                        "missing_count": 1,
                        "required_count": 2,
                        "status": "abnormal"
                    },
                    {
                        "date": "2024-01-03",
                        "clock_count": 2,
                        "normal_count": 1,
                        "makeup_count": 1,
                        "missing_count": 0,
                        "required_count": 2,
                        "status": "normal"
                    }
                ],
                "summary": {
                    "total_days": 3,
                    "normal_days": 2,
                    "abnormal_days": 1,
                    "total_clock_count": 5,
                    "total_missing_count": 1,
                    "total_makeup_count": 1
                }
            }
        ],
        "date_list": ["2024-01-01", "2024-01-02", "2024-01-03"],
        "daily_clock_count": 2,
        "date_range": {
            "start_date": "2024-01-01",
            "end_date": "2024-01-03",
            "days": 3
        },
        "pagination": {
            "total": 10,
            "per_page": 20,
            "current_page": 1,
            "last_page": 1
        }
    }
}
```

## 字段说明

### 用户信息
- `user_id`: 用户ID
- `user_name`: 用户名
- `true_name`: 真实姓名
- `department_id`: 部门ID
- `department_name`: 部门名称

### 每日统计 (daily_statistics)
- `date`: 日期
- `clock_count`: 当日总打卡次数（正常打卡+补卡）
- `normal_count`: 当日正常打卡次数
- `makeup_count`: 当日已完成补卡次数
- `missing_count`: 当日缺卡次数
- `required_count`: 当日应打卡次数
- `status`: 当日状态（normal-正常，abnormal-异常）

### 汇总统计 (summary)
- `total_days`: 统计总天数
- `normal_days`: 正常打卡天数
- `abnormal_days`: 异常打卡天数
- `total_clock_count`: 总打卡次数
- `total_missing_count`: 总缺卡次数
- `total_makeup_count`: 总补卡次数

## 使用示例

### 查询指定时间范围的所有用户
```bash
GET /admin/api/clock/statistics-report?start_date=2024-01-01&end_date=2024-01-07
```

### 查询指定部门的用户
```bash
GET /admin/api/clock/statistics-report?start_date=2024-01-01&end_date=2024-01-07&department_id=1
```

### 查询指定用户
```bash
GET /admin/api/clock/statistics-report?start_date=2024-01-01&end_date=2024-01-07&user_id=1
```

### 分页查询
```bash
GET /admin/api/clock/statistics-report?start_date=2024-01-01&end_date=2024-01-07&page=1&per_page=10
```

## 错误响应

### 日期范围超过31天
```json
{
    "code": 120006,
    "message": "查询日期范围不能超过31天"
}
```

### 参数验证错误
```json
{
    "code": 100007,
    "message": "开始日期不能为空"
}
```
