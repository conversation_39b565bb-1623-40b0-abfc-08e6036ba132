<?php

namespace App\Console;

use App\Console\Commands\CheckClockRecords;
use App\Console\Commands\SyncContactsCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * 定义应用的命令调度
     */
    protected function schedule(Schedule $schedule): void
    {
        // 每天凌晨1点执行打卡检查
        $schedule->command('clock:check')->dailyAt('01:00');

        // 每天凌晨2点同步部门和用户数据
        $schedule->command('sync:contacts')->dailyAt('02:00');
    }

    /**
     * 注册命令
     */
    protected function commands(): void
    {
        $this->load(__DIR__.'/Commands');

        require base_path('routes/console.php');
    }
}
