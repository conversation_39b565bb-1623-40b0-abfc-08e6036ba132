<?php

namespace App\Enums;

enum ErrorCodeEnum: int
{
    // 系统错误 (100000-100999)
    case SYSTEM_ERROR = 100000;
    case MODEL_NOT_FOUND = 100001;
    case NOT_FOUND = 100002;
    case METHOD_NOT_ALLOWED = 100003;
    case DATABASE_QUERY_EXCEPTION = 100004;
    case OTHER_HTTP_EXCEPTION = 100005;
    case HAS_SENSITIVE = 100006;
    case VALIDATE_ERROR = 100007;
    case INVALID_REQUEST = 100008;
    case PARTIAL_FAILURE = 100009;

    // 用户相关错误 (110000-110999)
    case USER_NOT_FOUND = 110001;
    case USER_STATUS_ERROR = 110002;
    case USER_PASSWORD_ERROR = 110003;
    case USER_EXIST = 110004;
    case USER_NOT_LOGIN = 110005;

    // 参数相关错误 (120000-120999)
    case PARAMS_MISSING = 120001;
    case ILLEGAL_PARAMS = 120005;
    case DATE_RANGE_TOO_LARGE = 120006;

    // 部门相关错误 (170000-170999)
    case DEPARTMENT_NOT_FOUND = 170001;
    case DEPARTMENT_HAS_CHILDREN = 170002;
    case DEPARTMENT_HAS_USERS = 170003;
    case DEPARTMENT_CODE_EXISTS = 170004;

    // 打卡系统错误 (180000-180999)
    case CLOCK_ALREADY_DONE = 180001;
    case CLOCK_LOCATION_INVALID = 180002;
    case CLOCK_TIME_INVALID = 180003;
    case CLOCK_COUNT_NOT_ENOUGH = 180004;
    case CLOCK_RECORD_NOT_FOUND = 180005;
    case MAKEUP_ALREADY_APPLIED = 180006;
    case MAKEUP_NOT_NEEDED = 180007;
    case MAKEUP_REVIEW_FAILED = 180008;
    case MAKEUP_ALREADY_REVIEWED = 180009;
    case CLOCK_TOO_FREQUENT = 180010;

    public function label(): string {
        return match ($this) {
            // 系统错误
            self::SYSTEM_ERROR => '请稍后再试',
            self::MODEL_NOT_FOUND => '未找到该项目',
            self::NOT_FOUND => '内容不存在',
            self::METHOD_NOT_ALLOWED => '不支持的请求方式',
            self::DATABASE_QUERY_EXCEPTION => '数据库查询异常',
            self::OTHER_HTTP_EXCEPTION => '其他网络错误',
            self::HAS_SENSITIVE => '输入信息违规,请修改或重新输入',
            self::VALIDATE_ERROR => '参数错误',
            self::INVALID_REQUEST => '无效的请求或参数',
            self::PARTIAL_FAILURE => '部分操作失败',

            // 用户相关错误
            self::USER_NOT_FOUND => '无此用户',
            self::USER_STATUS_ERROR => '用户状态异常',
            self::USER_PASSWORD_ERROR => '用户密码错误',
            self::USER_EXIST => '用户已存在',
            self::USER_NOT_LOGIN => '用户未登录',

            // 参数相关错误
            self::PARAMS_MISSING => '参数缺失',
            self::ILLEGAL_PARAMS => '非法参数',
            self::DATE_RANGE_TOO_LARGE => '日期范围过大',

            // 部门相关错误
            self::DEPARTMENT_NOT_FOUND => '部门不存在',
            self::DEPARTMENT_HAS_CHILDREN => '该部门下有子部门',
            self::DEPARTMENT_HAS_USERS => '该部门下有用户',
            self::DEPARTMENT_CODE_EXISTS => '部门编码已存在',

            // 打卡系统错误
            self::CLOCK_ALREADY_DONE => '今日已完成打卡',
            self::CLOCK_LOCATION_INVALID => '打卡位置无效',
            self::CLOCK_TIME_INVALID => '未在打卡时间范围内',
            self::CLOCK_COUNT_NOT_ENOUGH => '打卡次数不足',
            self::CLOCK_RECORD_NOT_FOUND => '未找到打卡记录',
            self::MAKEUP_ALREADY_APPLIED => '已申请补卡',
            self::MAKEUP_NOT_NEEDED => '无需补卡',
            self::MAKEUP_REVIEW_FAILED => '补卡审核失败',
            self::MAKEUP_ALREADY_REVIEWED => '补卡申请已审核',
            self::CLOCK_TOO_FREQUENT => '打卡过于频繁',
            default => '',
        };
    }
}
