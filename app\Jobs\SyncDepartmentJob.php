<?php

namespace App\Jobs;

use App\Models\Department;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SyncDepartmentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * 部门数据
     *
     * @var array
     */
    protected $department;

    /**
     * 创建新的任务实例
     *
     * @param array $department 单个部门数据
     */
    public function __construct(array $department)
    {
        $this->department = $department;
    }

    /**
     * 执行任务
     *
     * @return void
     */
    public function handle(): void
    {
        try {
            $department = $this->department;
            
            Department::updateOrCreate(
                ['code' => $department['code']],
                [
                    'name' => $department['name'],
                    'parent_id' => $department['parent'] ? $department['parent']['id'] : null,
                    'level' => $department['level'],
                ]
            );
            
            // 仅在出现问题时记录日志
        } catch (\Exception $e) {
            Log::error('部门同步失败', [
                'code' => $this->department['code'] ?? '未知',
                'name' => $this->department['name'] ?? '未知',
                'error' => $e->getMessage()
            ]);
            throw $e;
        }
    }
} 