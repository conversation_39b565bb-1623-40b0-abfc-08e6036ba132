APP_NAME=Laravel
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=http://localhost

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=sqlite
# DB_HOST=127.0.0.1
# DB_PORT=3306
# DB_DATABASE=laravel
# DB_USERNAME=root
# DB_PASSWORD=

SESSION_DRIVER=database
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database

CACHE_STORE=database
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Octane配置
OCTANE_SERVER=swoole
OCTANE_HTTPS=false
OCTANE_GARBAGE=50
OCTANE_MAX_EXECUTION_TIME=30
# 打卡系统配置
DAILY_CLOCK_COUNT=2
CLOCK_MORNING_START=00:00
CLOCK_MORNING_END=00:00
CLOCK_AFTERNOON_START=00:00
CLOCK_AFTERNOON_END=00:00

# JWT配置
JWT_SECRET_API=your_strong_random_api_user_secret_key_CHANGE_ME
JWT_ALGO_API=HS256
JWT_TTL_API=480 # API 用户 Token 有效期 (分钟)

JWT_SECRET_ADMIN=your_strong_random_admin_user_secret_key_CHANGE_ME
JWT_ALGO_ADMIN=HS256
JWT_TTL_ADMIN=120 # Admin 用户 Token 有效期 (分钟)

# 可选: 全局 tymon/jwt-auth 回退设置 (如果某些地方未使用我们定制的 guard)
JWT_SECRET=your_default_fallback_secret_key_CHANGE_ME
JWT_TTL=60
JWT_ALGO=HS256

CHANGGUAN_CLIENT_ID=
CHANGGUAN_ACCESS_KEY=
CHANGGUAN_ACCESS_SECRET=
CHANGGUAN_PUSH_URL=
CHANGGUAN_OAUTH_URL=

# 后台常观SDK配置
CHANGGUAN_ADMIN_CLIENT_ID=
CHANGGUAN_ADMIN_ACCESS_KEY=
CHANGGUAN_ADMIN_ACCESS_SECRET=
