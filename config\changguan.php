<?php

return [
    /*
    |--------------------------------------------------------------------------
    | 默认项目
    |--------------------------------------------------------------------------
    |
    | 这里设置默认使用的项目。当没有指定具体项目时，将使用此处配置的项目。
    | 你可以根据需要修改此值。
    |
    */

    'default' => env('CHANGGUAN_PROJECT', 'main'),

    /*
    |--------------------------------------------------------------------------
    | 常观项目配置
    |--------------------------------------------------------------------------
    |
    | 在这里你可以配置所有常观SDK项目的连接信息。每个项目可以有自己的客户端
    | 凭证和API端点。通过项目名称可以随时切换不同的配置。
    |
    */

    'projects' => [
        'main' => [
            'client_id' => env('CHANGGUAN_CLIENT_ID'),
            'client_access_key' => env('CHANGGUAN_ACCESS_KEY'),
            'client_access_secret' => env('CHANGGUAN_ACCESS_SECRET'),
            'push_base_url' => env('CHANGGUAN_PUSH_URL', 'https://api.changguan.com'),
            'oauth_base_url' => env('CHANGGUAN_OAUTH_URL', 'https://oauth.changguan.com'),
        ],

        // 后台管理项目配置
        'admin' => [
            'client_id' => env('CHANGGUAN_ADMIN_CLIENT_ID'),
            'client_access_key' => env('CHANGGUAN_ADMIN_ACCESS_KEY'),
            'client_access_secret' => env('CHANGGUAN_ADMIN_ACCESS_SECRET'),
            'push_base_url' => env('CHANGGUAN_PUSH_URL', 'https://api.changguan.com'),
            'oauth_base_url' => env('CHANGGUAN_OAUTH_URL', 'https://oauth.changguan.com'),
        ],
    ],
];
