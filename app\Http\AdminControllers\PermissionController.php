<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class PermissionController extends Controller
{
    /**
     * 获取所有权限列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getPermissions(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            // 'guard_name' => 'nullable|string|in:admin,api',
            'keyword' => 'nullable|string|max:50',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'keyword.string' => '关键词格式错误',
            'keyword.max' => '关键词长度不能超过50个字符',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        // 获取查询参数
        $guardName = 'admin';
        $keyword = $request->input('keyword');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        // 构建查询
        $query = Permission::query();

        // 按守卫名称筛选
        $query->where('guard_name', $guardName);

        // 按关键词搜索
        if ($keyword) {
            $query->where('name', 'like', "%{$keyword}%");
        }

        // 排序
        $query->orderBy('id', 'asc');

        // 分页获取权限
        $permissions = $query->paginate($perPage, ['*'], 'page', $page);

        return Respond::success($permissions);
    }

    /**
     * 获取权限详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getPermissionDetail(int $id): JsonResponse
    {
        $permission = Permission::find($id);

        if (!$permission) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND, '权限不存在');
        }

        // 获取拥有此权限的角色
        $roles = Role::whereHas('permissions', function ($query) use ($id) {
            $query->where('id', $id);
        })->get(['id', 'name']);

        $result = [
            'id' => $permission->id,
            'name' => $permission->name,
            'guard_name' => $permission->guard_name,
            'created_at' => $permission->created_at->toDateTimeString(),
            'updated_at' => $permission->updated_at->toDateTimeString(),
            'roles' => $roles,
        ];

        return Respond::success($result);
    }

    /**
     * 创建新权限
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createPermission(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'name' => 'required|string|max:255|unique:permissions,name',
            // 'guard_name' => 'required|string|in:admin,api',
        ],[
            'name.required' => '权限名称不能为空',
            'name.string' => '权限名称格式错误',
            'name.max' => '权限名称长度不能超过255个字符',
            'name.unique' => '权限名称已存在',
        ]);

        // 创建权限
        $permission = Permission::create([
            'name' => $request->input('name'),
            'guard_name' => 'admin',
        ]);

        return Respond::success([
            'id' => $permission->id,
            'name' => $permission->name,
            'guard_name' => $permission->guard_name,
        ], '权限创建成功');
    }

    /**
     * 更新权限
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updatePermission(Request $request, int $id): JsonResponse
    {
        $permission = Permission::find($id);

        if (!$permission) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND, '权限不存在');
        }

        // 验证请求参数
        $this->validate($request, [
            'name' => 'required|string|max:255|unique:permissions,name,' . $id,
            // 'guard_name' => 'required|string|in:admin,api',
        ],[
            'name.required' => '权限名称不能为空',
            'name.string' => '权限名称格式错误',
            'name.max' => '权限名称长度不能超过255个字符',
            'name.unique' => '权限名称已存在',
        ]);

        // 更新权限
        $permission->name = $request->input('name');
        $permission->save();

        return Respond::success([
            'id' => $permission->id,
            'name' => $permission->name,
            'guard_name' => $permission->guard_name,
        ], '权限更新成功');
    }

    /**
     * 删除权限
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deletePermission(int $id): JsonResponse
    {
        $permission = Permission::find($id);

        if (!$permission) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND, '权限不存在');
        }

        // 检查是否有角色使用此权限
        $roleCount = Role::whereHas('permissions', function ($query) use ($id) {
            $query->where('id', $id);
        })->count();

        if ($roleCount > 0) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, '该权限已被角色使用，无法删除');
        }

        // 删除权限
        $permission->delete();

        return Respond::success(null, '权限删除成功');
    }
}
