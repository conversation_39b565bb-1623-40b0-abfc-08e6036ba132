<?php

namespace App\Http\AdminControllers;

use App\Enums\ErrorCodeEnum;
use App\Http\Controllers\Controller;
use App\Models\AdminUser;
use App\Utils\Respond;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class RoleController extends Controller
{
    /**
     * 获取角色列表
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getRoles(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'guard_name' => 'nullable|string|in:admin,api',
            'keyword' => 'nullable|string|max:50',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'guard_name.string' => '守卫名称格式错误',
            'guard_name.in' => '守卫名称值错误',
            'keyword.string' => '关键词格式错误',
            'keyword.max' => '关键词长度不能超过50个字符',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        // 获取查询参数
        $guardName = $request->input('guard_name', 'admin');
        $keyword = $request->input('keyword');
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        // 构建查询
        $query = Role::query();

        // 按守卫名称筛选
        $query->where('guard_name', $guardName);

        // 按关键词搜索
        if ($keyword) {
            $query->where('name', 'like', "%{$keyword}%");
        }

        // 排序
        $query->orderBy('id', 'asc');

        // 分页获取角色
        $roles = $query->paginate($perPage, ['*'], 'page', $page);

        // 获取每个角色的权限数量和用户数量
        foreach ($roles as $role) {
            $role->permission_count = $role->permissions()->count();
            $role->user_count = AdminUser::role($role->name)->count();
        }

        return Respond::success($roles);
    }

    /**
     * 获取角色详情
     *
     * @param int $id
     * @return JsonResponse
     */
    public function getRoleDetail(int $id): JsonResponse
    {
        $role = Role::with('permissions')->find($id);

        if (!$role) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND, '角色不存在');
        }

        // 获取拥有此角色的用户数量
        $userCount = AdminUser::role($role->name)->count();

        $result = [
            'id' => $role->id,
            'name' => $role->name,
            'guard_name' => $role->guard_name,
            'created_at' => $role->created_at->toDateTimeString(),
            'updated_at' => $role->updated_at->toDateTimeString(),
            'permissions' => $role->permissions,
            'user_count' => $userCount,
        ];

        return Respond::success($result);
    }

    /**
     * 创建新角色
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function createRole(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'name' => 'required|string|max:255|unique:roles,name',
            // 'guard_name' => 'required|string|in:admin,api',
            'permissions' => 'nullable|array',
            'permissions.*' => 'integer|exists:permissions,id',
        ],[
            'name.required' => '角色名称不能为空',
            'name.string' => '角色名称格式错误',
            'name.max' => '角色名称长度不能超过255个字符',
            'name.unique' => '角色名称已存在',
            'permissions.array' => '权限格式错误',
            'permissions.*.integer' => '权限ID格式错误',
            'permissions.*.exists' => '权限不存在',
        ]);

        // 创建角色
        $role = Role::create([
            'name' => $request->input('name'),
            'guard_name' => 'admin',
        ]);

        // 分配权限
        if ($request->has('permissions') && is_array($request->input('permissions'))) {
            $permissions = Permission::whereIn('id', $request->input('permissions'))->get();
            $role->syncPermissions($permissions);
        }

        return Respond::success([
            'id' => $role->id,
            'name' => $role->name,
            'guard_name' => $role->guard_name,
        ], '角色创建成功');
    }

    /**
     * 更新角色
     *
     * @param Request $request
     * @param int $id
     * @return JsonResponse
     */
    public function updateRole(Request $request, int $id): JsonResponse
    {
        $role = Role::find($id);

        if (!$role) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND, '角色不存在');
        }

        // 验证请求参数
        $this->validate($request, [
            'name' => 'required|string|max:255|unique:roles,name,' . $id,
            // 'guard_name' => 'required|string|in:admin,api',
            'permissions' => 'nullable|array',
            'permissions.*' => 'integer|exists:permissions,id',
        ],[
            'name.required' => '角色名称不能为空',
            'name.string' => '角色名称格式错误',
            'name.max' => '角色名称长度不能超过255个字符',
            'name.unique' => '角色名称已存在',
            'permissions.array' => '权限格式错误',
            'permissions.*.integer' => '权限ID格式错误',
            'permissions.*.exists' => '权限不存在',
        ]);

        // 更新角色
        $role->name = $request->input('name');
        $role->guard_name = 'admin';
        $role->save();

        // 更新权限
        if ($request->has('permissions')) {
            $permissions = Permission::whereIn('id', $request->input('permissions'))->get();
            $role->syncPermissions($permissions);
        }

        return Respond::success([
            'id' => $role->id,
            'name' => $role->name,
            'guard_name' => $role->guard_name,
        ], '角色更新成功');
    }

    /**
     * 删除角色
     *
     * @param int $id
     * @return JsonResponse
     */
    public function deleteRole(int $id): JsonResponse
    {
        $role = Role::find($id);

        if (!$role) {
            return Respond::error(ErrorCodeEnum::NOT_FOUND, '角色不存在');
        }

        // 检查是否有用户使用此角色
        $userCount = AdminUser::role($role->name)->count();

        if ($userCount > 0) {
            return Respond::error(ErrorCodeEnum::VALIDATE_ERROR, '该角色已被用户使用，无法删除');
        }

        // 删除角色
        $role->delete();

        return Respond::success(null, '角色删除成功');
    }

    /**
     * 为用户分配角色
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function assignRoleToUser(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'user_id' => 'required|integer|exists:users,id',
            'roles' => 'required|array',
            'roles.*' => 'integer|exists:roles,id',
        ],[
            'user_id.required' => '用户ID不能为空',
            'user_id.integer' => '用户ID格式错误',
            'user_id.exists' => '用户不存在',
            'roles.required' => '角色不能为空',
            'roles.array' => '角色格式错误',
            'roles.*.integer' => '角色ID格式错误',
            'roles.*.exists' => '角色不存在',
        ]);

        $userId = $request->input('user_id');
        $roleIds = $request->input('roles');

        // 获取用户
        $user = AdminUser::find($userId);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        // 获取角色
        $roles = Role::whereIn('id', $roleIds)->where('guard_name', 'admin')->get();

        // 同步角色
        $user->syncRoles($roles);

        return Respond::success(null, '角色分配成功');
    }

    /**
     * 获取用户的角色
     *
     * @param int $userId
     * @return JsonResponse
     */
    public function getUserRoles(int $userId): JsonResponse
    {
        $user = AdminUser::find($userId);

        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_FOUND, '用户不存在');
        }

        $roles = $user->roles()->get(['id', 'name', 'guard_name']);

        return Respond::success($roles);
    }
}
