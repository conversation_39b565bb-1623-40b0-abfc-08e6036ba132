<?php

namespace App\Http\Controllers;

use App\Enums\ErrorCodeEnum;
use App\Models\ClockRecord;
use App\Models\User;
use App\Services\LocationService;
use App\Utils\Respond;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class ClockController extends Controller
{

    public function clockInStatus()
    {
        $user = Auth::guard('api')->user();

        $clockCount = ClockRecord::ofUser($user->id)
            ->whereDate('clock_time', Carbon::now()->toDateString())
            ->where('status', ClockRecord::STATUS_NORMAL)
            ->count();

        $dailyClockCount = config('clock.daily_count', 2);

        // 今日打卡记录
        $todayClockRecords = ClockRecord::ofUser($user->id)
            ->whereDate('clock_time', Carbon::now()->toDateString())
            ->where('status', ClockRecord::STATUS_NORMAL)
            ->orderBy('clock_time', 'desc')
            ->get();

        // 检查当前是否在有效打卡时间范围内
        $now = Carbon::now();
        $currentTime = $now->format('H:i');

        // 获取配置的打卡时间范围
        $morningStart = config('clock.morning_start', '00:00');
        $morningEnd = config('clock.morning_end', '00:00');
        $afternoonStart = config('clock.afternoon_start', '00:00');
        $afternoonEnd = config('clock.afternoon_end', '00:00');

        // 检查是否在有效打卡时间范围内
        $isValidTime = false;
        $currentPeriod = null;

        // 如果配置了上午打卡时间范围
        if ($morningStart !== '00:00' && $morningEnd !== '00:00') {
            if ($currentTime >= $morningStart && $currentTime <= $morningEnd) {
                $isValidTime = true;
                $currentPeriod = 'morning';
            }
        }

        // 如果配置了下午打卡时间范围
        if ($afternoonStart !== '00:00' && $afternoonEnd !== '00:00') {
            if ($currentTime >= $afternoonStart && $currentTime <= $afternoonEnd) {
                $isValidTime = true;
                $currentPeriod = 'afternoon';
            }
        }

        // 如果都没有配置时间范围，则允许全天打卡
        if ($morningStart === '00:00' && $morningEnd === '00:00' &&
            $afternoonStart === '00:00' && $afternoonEnd === '00:00') {
            $isValidTime = true;
            $currentPeriod = 'all_day';
        }

        // 获取位置配置信息
        $locationService = new LocationService();
        $locationConfig = $locationService->getClockLocationConfig();

        return Respond::success([
            'status' => $clockCount >= $dailyClockCount,
            'count' => $clockCount,
            'today_clock_records' => $todayClockRecords,
            'is_valid_time' => $isValidTime,
            'current_period' => $currentPeriod,
            'time_ranges' => [
                'morning' => [
                    'start' => $morningStart,
                    'end' => $morningEnd,
                ],
                'afternoon' => [
                    'start' => $afternoonStart,
                    'end' => $afternoonEnd,
                ],
            ],
            'location_config' => $locationConfig,
        ]);
    }

    /**
     * 打卡
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function clockIn(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'latitude' => 'required|numeric',
            'longitude' => 'required|numeric',
        ],[
            'latitude.required' => '打卡位置不能为空',
            'longitude.required' => '打卡位置不能为空',
        ]);

        // 获取当前用户
        $user = Auth::guard('api')->user();
        // if (!$user) {
        //     return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN);
        // }

        // 验证打卡时间范围
        $now = Carbon::now();
        $currentTime = $now->format('H:i');

        // 获取配置的打卡时间范围
        $morningStart = config('clock.morning_start', '00:00');
        $morningEnd = config('clock.morning_end', '00:00');
        $afternoonStart = config('clock.afternoon_start', '00:00');
        $afternoonEnd = config('clock.afternoon_end', '00:00');

        // 检查是否在有效打卡时间范围内
        $isValidTime = false;

        // 如果配置了上午打卡时间范围
        if ($morningStart !== '00:00' && $morningEnd !== '00:00') {
            if ($currentTime >= $morningStart && $currentTime <= $morningEnd) {
                $isValidTime = true;
            }
        }

        // 如果配置了下午打卡时间范围
        if ($afternoonStart !== '00:00' && $afternoonEnd !== '00:00') {
            if ($currentTime >= $afternoonStart && $currentTime <= $afternoonEnd) {
                $isValidTime = true;
            }
        }

        // 如果都没有配置时间范围，则允许全天打卡
        if ($morningStart === '00:00' && $morningEnd === '00:00' &&
            $afternoonStart === '00:00' && $afternoonEnd === '00:00') {
            $isValidTime = true;
        }

        // 如果不在有效时间范围内，返回错误
        if (!$isValidTime) {
            return Respond::error(ErrorCodeEnum::CLOCK_TIME_INVALID);
        }

        // 验证打卡位置
        $locationService = new LocationService();
        $userLatitude = (float) $request->input('latitude');
        $userLongitude = (float) $request->input('longitude');

        // 验证坐标是否有效
        if (!$locationService->isValidCoordinate($userLatitude, $userLongitude)) {
            return Respond::error(ErrorCodeEnum::CLOCK_LOCATION_INVALID, '打卡位置坐标无效');
        }

        // 验证打卡位置是否在允许范围内
        $locationValidation = $locationService->validateClockLocation($userLatitude, $userLongitude);
        if (!$locationValidation['valid']) {
            return Respond::error(ErrorCodeEnum::CLOCK_LOCATION_INVALID, $locationValidation['message']);
        }

        // 获取连续打卡时间间隔
        $continuousClockInterval = config('clock.continuous_clock_interval', 1);
        $continuousClockInterval = $continuousClockInterval * 60;

        // 获取当前日期
        $today = Carbon::now()->toDateString();


        // 验证上次打卡时间
        $lastClockRecord = ClockRecord::ofUser($user->id)
            ->whereDate('clock_time', $today)
            ->orderBy('clock_time', 'desc')
            ->first();

        if ($lastClockRecord) {
            $lastClockTime = $lastClockRecord->clock_time;
            $diffInMinutes = $now->diffInMinutes($lastClockTime,true);
            if ($diffInMinutes < $continuousClockInterval) {
                return Respond::error(ErrorCodeEnum::CLOCK_TOO_FREQUENT);
            }
        }


        // 获取今日已打卡次数
        $clockCount = ClockRecord::ofUser($user->id)
            ->whereDate('clock_time', $today)
            ->where('status', ClockRecord::STATUS_NORMAL)
            ->count();

        // // 获取每日需要打卡的次数
        // $dailyClockCount = config('clock.daily_count', 2);

        // // 如果已经完成了今日打卡，返回错误
        // if ($clockCount >= $dailyClockCount) {
        //     return Respond::error(ErrorCodeEnum::CLOCK_ALREADY_DONE);
        // }

        // 创建打卡记录
        $clockRecord = new ClockRecord([
            'user_id' => $user->id,
            'clock_time' => $now,
            'latitude' => $request->input('latitude'),
            'longitude' => $request->input('longitude'),
            'status' => ClockRecord::STATUS_NORMAL,
            'apply_status' => ClockRecord::APPLY_STATUS_NONE,
        ]);

        $clockRecord->save();

        // 返回打卡成功信息
        return Respond::success([
            'id' => $clockRecord->id,
            'clock_time' => $clockRecord->clock_time->toDateTimeString(),
            'count' => $clockCount + 1,
        ], '打卡成功');
    }

    /**
     * 申请补卡
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function applyForMakeup(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'clock_id' => 'required|integer',
            'reason' => 'required|string|max:255',
        ],[
            'clock_id.required' => '打卡记录不能为空',
            'clock_id.integer' => '打卡记录格式错误',
            'reason.required' => '补卡原因不能为空',
            'reason.string' => '补卡原因格式错误',
            'reason.max' => '补卡原因长度不能超过255个字符',
        ]);

        // 获取当前用户
        $user = Auth::user();

        $clockId = $request->input('clock_id');
        $reason = $request->input('reason');

        $clockRecord = ClockRecord::ofUser($user->id)
            ->where('id', $clockId)
            ->where('status', ClockRecord::STATUS_MAKEUP)
            ->where('apply_status', ClockRecord::APPLY_STATUS_PENDING)
            ->first();

        if (!$clockRecord) {
            return Respond::error(ErrorCodeEnum::CLOCK_RECORD_NOT_FOUND);
        }

        $clockRecord->apply_status = ClockRecord::APPLY_STATUS_COMPLETED;
        $clockRecord->apply_time = Carbon::now();
        $clockRecord->apply_reason = $reason;
        $clockRecord->save();

        return Respond::success(null, '补卡申请提交成功，等待审核');
    }

    // 需要补卡的记录
    public function getMakeupRecords(Request $request): JsonResponse
    {
        $this->validate($request, [
            'date' => 'required|date_format:Y-m',
        ],[
            'date.required' => '日期不能为空',
            'date.date_format' => '日期格式错误',
        ]);

        $user = Auth::user();
        // 按月查询需要补卡的记录
        $records = ClockRecord::ofUser($user->id)
            ->ofMonth($request->input('date'))
            ->where('status', ClockRecord::STATUS_MAKEUP)
            ->where('apply_status', ClockRecord::APPLY_STATUS_PENDING)
            ->orderBy('clock_time', 'asc')
            ->get();


        return Respond::success($records);
    }

    // 补卡记录
    public function getAppliedMakeupRecords(Request $request): JsonResponse
    {
        $this->validate($request, [
            'date' => 'required|date_format:Y-m',
        ],[
            'date.required' => '日期不能为空',
            'date.date_format' => '日期格式错误',
        ]);

        $user = Auth::user();

        // 按月查询补卡记录
        $records = ClockRecord::ofUser($user->id)
            ->ofMonth($request->input('date'))
            ->where('status', ClockRecord::STATUS_MAKEUP)
            ->whereIn('apply_status', [ClockRecord::APPLY_STATUS_APPLIED, ClockRecord::APPLY_STATUS_COMPLETED])
            ->orderBy('clock_time', 'asc')
            ->paginate();

        return Respond::success($records);
    }
    /**
     * 获取打卡记录
     *
     * @param Request $request
     * @return JsonResponse
     */
    public function getClockRecords(Request $request): JsonResponse
    {
        // 验证请求参数
        $this->validate($request, [
            'month' => 'nullable|date_format:Y-m',
            'page' => 'nullable|integer|min:1',
            'per_page' => 'nullable|integer|min:1|max:100',
        ],[
            'month.date_format' => '日期格式错误',
            'page.integer' => '页码格式错误',
            'page.min' => '页码不能小于1',
            'per_page.integer' => '每页数量格式错误',
            'per_page.min' => '每页数量不能小于1',
            'per_page.max' => '每页数量不能大于100',
        ]);

        // 获取当前用户
        $user = Auth::user();
        if (!$user) {
            return Respond::error(ErrorCodeEnum::USER_NOT_LOGIN);
        }

        // 获取查询参数
        $month = $request->input('month', Carbon::now()->format('Y-m'));
        $page = $request->input('page', 1);
        $perPage = $request->input('per_page', 20);

        // 查询打卡记录
        $query = ClockRecord::ofUser($user->id)
            ->ofMonth($month)
            ->orderBy('clock_time', 'asc');

        $records = $query->paginate($perPage, ['*'], 'page', $page);

        // 获取每日需要打卡的次数
        $dailyClockCount = config('clock.daily_count', 2);

        // 处理返回数据
        $result = [];
        $recordsByDate = [];

        // 按日期分组
        foreach ($records as $record) {
            $date = $record->clock_time->toDateString();
            if (!isset($recordsByDate[$date])) {
                $recordsByDate[$date] = [];
            }
            $recordsByDate[$date][] = $record;
        }

        // 处理每一天的数据
        foreach ($recordsByDate as $date => $dateRecords) {
            $normalCount = 0;
            $makeupCount = 0;
            $pendingMakeup = false;
            $records = [];

            foreach ($dateRecords as $record) {
                if ($record->status == ClockRecord::STATUS_NORMAL) {
                    $normalCount++;
                } elseif ($record->status == ClockRecord::STATUS_MAKEUP) {
                    $makeupCount++;
                    if ($record->apply_status == ClockRecord::APPLY_STATUS_PENDING) {
                        $pendingMakeup = true;
                    }
                }

                $records[] = [
                    'id' => $record->id,
                    'clock_time' => $record->clock_time->toDateTimeString(),
                    'status' => $record->status,
                    'status_text' => $record->status_text,
                    'apply_status' => $record->apply_status,
                    'apply_status_text' => $record->apply_status_text,
                    'latitude' => $record->latitude,
                    'longitude' => $record->longitude,
                    'apply_time' => $record->apply_time ? $record->apply_time->toDateTimeString() : null,
                    'apply_reason' => $record->apply_reason,
                    'review_time' => $record->review_time ? $record->review_time->toDateTimeString() : null,
                    'review_remark' => $record->review_remark,
                ];
            }

            $result[] = [
                'date' => $date,
                'normal_count' => $normalCount,
                'makeup_count' => $makeupCount,
                'total_count' => $normalCount + $makeupCount,
                'required_count' => $dailyClockCount,
                'is_completed' => ($normalCount + $makeupCount) >= $dailyClockCount,
                'need_makeup' => ($normalCount + $makeupCount) < $dailyClockCount,
                'pending_makeup' => $pendingMakeup,
                'records' => $records,
            ];
        }

        // result 

        return Respond::success($result);
    }
}
